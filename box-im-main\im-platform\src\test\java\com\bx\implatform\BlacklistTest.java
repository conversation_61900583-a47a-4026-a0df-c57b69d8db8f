package com.bx.implatform;

import com.bx.implatform.api.UserApiTest;

import java.util.HashMap;
import java.util.Map;

public class BlacklistTest extends BaseTest{

    public static void test(){
        String token = UserApiTest.token("1").getAccessToken();
        login(token);
//        list();
//        add(20L);
//        add(21L);
//        list();
//        delete(13L);
//        list();
    }

    public static void delete(Long id){
        delete("/blacklist/remove/{id}", null, id);
    }

    public static void add(Long id){
        Map<String,Object> map = new HashMap<>();
        map.put("userId", id);
        post("/blacklist/add", map);
    }

    public static void list(){
        get("/blacklist/list", null);
    }
}
