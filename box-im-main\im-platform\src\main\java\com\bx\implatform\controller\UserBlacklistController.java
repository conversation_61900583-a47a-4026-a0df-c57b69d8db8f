package com.bx.implatform.controller;

import com.bx.implatform.annotation.RepeatSubmit;
import com.bx.implatform.controller.param.UserBlacklistParam;
import com.bx.implatform.enums.ResultCode;
import com.bx.implatform.exception.GlobalException;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.bx.implatform.service.UserBlacklistService;
import com.bx.implatform.session.SessionContext;
import com.bx.implatform.vo.RelationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import jakarta.websocket.server.PathParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: Blue
 * @date: 2024-09-22
 * @version: 1.0
 */
@Tag(name = "用户黑名单")
@RestController
@RequestMapping("/blacklist")
@RequiredArgsConstructor
public class UserBlacklistController {

    private final UserBlacklistService userBlacklistService;

    @RepeatSubmit
    @PostMapping("/add")
    @Operation(summary = "加入黑名单", description = "加入黑名单")
    public Result add(@RequestBody UserBlacklistParam param) {
        userBlacklistService.add(SessionContext.getSession().getUserId(), param.getUserId());
        return ResultUtils.success();
    }

    @DeleteMapping("/remove/{userId}")
    @Operation(summary = "移除黑名单", description = "移除黑名单")
    public Result remove(@NotNull(message = "用户id不可为空") @PathVariable Long userId) {
        userBlacklistService.remove(SessionContext.getSession().getUserId(), userId);
        return ResultUtils.success();
    }

    @GetMapping("/list")
    @Operation(summary = "黑名单列表", description = "黑名单列表")
    public Result<List<RelationVO>> list() {
        return ResultUtils.success(userBlacklistService.list(SessionContext.getSession().getUserId()));
    }
}
