package com.bx.implatform.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "发送短信验证码DTO")
public class ThirdUserDataDTO {
    @Schema(description = "账号")
    private String account;
    @Schema(description = "用户昵称")
    private String nickname;
    @Schema(description = "手机号")
    private String mobile;
    @Schema(description = "是否游客")
    private Integer tourist;
    @Schema(description = "机器人")
    private Integer robot;
    @Schema(description = "来源论坛id")
    private Integer forum_id;
    @Schema(description = "游戏id")
    private String gameid;
    @Schema(description = "用户平台")
    private String plat;
    @Schema(description = "IP")
    private String ip;
    @Schema(description = "创建时间")
    private String create_time;
    @Schema(description = "最后登录时间")
    private String last_login_time;
    @Schema(description = "用户是否被封号")
    private Integer sealed;
    @Schema(description = "用户是否被禁言")
    private Integer silenced;
    @Schema(description = "钱")
    private Long money;
    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "头像")
    private String avatar;
    @Schema(description = "点赞数")
    private Integer votes;
    @Schema(description = "VIP等级, 0:普通用户, 1及以上:VIP等级")
    private Integer vip;
    @Schema(description = "文章数")
    private Integer articleCount;
    @Schema(description = "关注数")
    private Integer fellow;
    @Schema(description = "粉丝数")
    private Integer fans;

}
