const MESSAGE_TYPE = {
	TEXT: 0,
	IMAGE: 1,
	FILE: 2,
	AUDIO: 3,
	VIDEO: 4,
	USER_CARD: 5,
	GROUP_CARD: 6,
	RECALL: 10,
	READED: 11,
	RECEIPT: 12,
	TIP_TIME: 20,
	TIP_TEXT: 21,
	LOADING: 30,
	ACT_RT_VOICE: 40,
	ACT_RT_VIDEO: 41,
	USER_BANNED: 50,
	SYSTEM_MESSAGE: 53,
	USER_UNREG: 54,
	FRIEND_REQ_APPLY: 70,
	FRIEND_REQ_APPROVE: 71,
	FRIEND_REQ_REJECT: 72,
	FRIEND_REQ_RECALL: 73,
	FRIEND_NEW: 80,
	FRIEND_DEL: 81,
	FRIEND_ONLINE: 82,
	FRIEND_DND: 83,
	FRIEND_TOP: 84,
	GROUP_NEW: 90,
	GROUP_DEL: 91,
	GROUP_TOP_MESSAGE: 92,
	G<PERSON><PERSON>_DND: 93,
	G<PERSON><PERSON>_TOP: 94,
	<PERSON><PERSON><PERSON>_ALL_MUTED: 95,
	G<PERSON><PERSON>_MEMBER_MUTED: 96,
	RTC_SETUP_VOICE: 100,
	RTC_SETUP_VIDEO: 101,
	RTC_ACCEPT: 102,
	RTC_REJECT: 103,
	RTC_CANCEL: 104,
	RTC_FAILED: 105,
	RTC_HANDUP: 106,
	RTC_OFFER: 107,
	RTC_ANSWER: 108,
	RTC_CANDIDATE: 109,
	RTC_GROUP_SETUP: 200,
	RTC_GROUP_ACCEPT: 201,
	RTC_GROUP_REJECT: 202,
	RTC_GROUP_FAILED: 203,
	RTC_GROUP_CANCEL: 204,
	RTC_GROUP_QUIT: 205,
	RTC_GROUP_INVITE: 206,
	RTC_GROUP_JOIN: 207,
	RTC_GROUP_OFFER: 208,
	RTC_GROUP_ANSWER: 209,
	RTC_GROUP_CANDIDATE: 210,
	RTC_GROUP_DEVICE: 211
}

const USER_STATE = {
	OFFLINE: 0,
	FREE: 1,
	BUSY: 2
}

const TERMINAL_TYPE = {
	WEB: 0,
	APP: 1
}

const MESSAGE_STATUS = {
	FAILED: -2, // 发送失败
	SENDING: -1, // 发送中(消息没到服务器)
	PENDING: 0, // 未送达(消息已到服务器，但对方没收到)
	DELIVERED: 1, // 已送达(对方已收到，但是未读消息)
	RECALL: 2, // 已撤回
	READED: 3, // 消息已读
}

const REQUEST_STATUS = {
	PENDING: 1, //"待处理"
	APPROVED: 2, //"同意"
	REJECTE: 3, //"拒绝"
	EXPIRED: 4 // 过期
}

export {
	MESSAGE_TYPE,
	USER_STATE,
	TERMINAL_TYPE,
	MESSAGE_STATUS,
	REQUEST_STATUS
}