package com.bx.implatform;

import com.bx.implatform.api.UserApiTest;

public class MainTest extends BaseTest{

    public static String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHBpcmUiOjE3NTc1NjIxMTUuNjQyOTIyLCJzZXNzaW9uX2tleSI6IiIsImFjY291bnQiOiJPNjgzUUdDTiIsIm5pY2tuYW1lIjoi6Zyc5Y2O55yf6I606IujIiwiYXZhdGFyIjoiaGVhZDAwOC5wbmcifQ.fp7NhINwPgVKG5ZuCwtyqhxTOLsGzTCaCTEZeWHTr-0";

    public static void main(String[] args) {
        login(token);
//        test();
//        ContactsTest.test();
//        FriendRequestTest.test();
//        BlacklistTest.test();
//        UserTest.test();
        LoginTest.refreshToken();
    }
    public static void test() {
//        ContactsTest.friendList();
//        FriendTest.delete(23L);
//        ContactsTest.friendList();
    }
}
