package com.bx.implatform.service;

import com.bx.implatform.api.dto.ApiFollowDTO;
import com.bx.implatform.vo.FriendVO;
import com.bx.implatform.vo.RelationVO;

import java.util.List;


public interface FollowService{

    void follow(String userId, String targetUserId);

    void unfollow(String userId, String targetUserId);

    List<RelationVO> followings(String userName);

    List<ApiFollowDTO> follows(List<ApiFollowDTO> follows);

    List<ApiFollowDTO> unfollows(List<ApiFollowDTO> follows);
}