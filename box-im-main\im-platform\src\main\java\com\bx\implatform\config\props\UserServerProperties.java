package com.bx.implatform.config.props;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Component
public class UserServerProperties {

    @Value("${userServer.host}")
    private String host;

    @Value("${userServer.path.userInfo}")
    private String url_userInfo;

    public String getUrl_userInfo() {
        return host + url_userInfo;
    }
}
