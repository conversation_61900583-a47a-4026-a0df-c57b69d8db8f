package com.bx.implatform;

import com.bx.implatform.dto.PrivateMessageDTO;

public class PrivateMessageTest extends BaseTest{

    /**
     * 消息类型 0:文字 1:图片 2:文件 3:语音 4:视频
     */
    public static void send(Long recvId, String content, Integer type){
        PrivateMessageDTO data = new PrivateMessageDTO();
        data.setRecvId(recvId);
        data.setContent(content);
        data.setType(type);
        post("/message/private/send", data);
    }
}
