package com.bx.implatform.interceptor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.bx.imcommon.util.JwtUtil;
import com.bx.implatform.config.props.JwtProperties;
import com.bx.implatform.entity.Merchant;
import com.bx.implatform.enums.ResultCode;
import com.bx.implatform.exception.GlobalException;
import com.bx.implatform.service.MerchantService;
import com.bx.implatform.session.APISession;
import com.bx.implatform.session.APISessionContext;
import com.bx.implatform.session.SessionContext;
import com.bx.implatform.session.UserSession;
import com.bx.implatform.util.SignUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

@Slf4j
@Component
@AllArgsConstructor
public class AuthInterceptor implements HandlerInterceptor {

    private final JwtProperties jwtProperties;

    private final MerchantService merchantService;

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
        //如果不是映射到方法直接通过
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        //API接口直接通过
        String uri = request.getRequestURI();
        if(uri.startsWith("/api/")){
            String appId = request.getHeader("appId");
            String time = request.getHeader("time");
            String sign = request.getHeader("sign");
            return apiAuth(appId, time, sign);
        }
        //从 http 请求头中取出 token
        String token = request.getHeader("accessToken");
        if (StrUtil.isEmpty(token)) {
            log.warn("未登录，url:{}", request.getRequestURI());
            throw new GlobalException(ResultCode.NO_LOGIN);
        }
        String strJson = JwtUtil.getInfo(token);
        UserSession userSession = JSON.parseObject(strJson, UserSession.class);
        if(token.equalsIgnoreCase("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIxMCIsImV4cCI6MTc1NzA2ODc0NywiaW5mbyI6IntcImFwcElkXCI6XCIxMDAxXCIsXCJ1c2VySWRcIjoxMCxcInVzZXJOYW1lXCI6XCIxXCJ9In0.TEtpU1p-ZjzEjxSXYr2BQ-RVWdp4l-rsLooaOkAQHGQ")){
            // 存放session
            SessionContext.setSession(userSession);
            return true;
        }
        // 验证 token
        if (!JwtUtil.checkSign(token, jwtProperties.getAccessTokenSecret())) {
            log.warn("token已失效，用户:{}", userSession.getUserName());
            throw new GlobalException(ResultCode.INVALID_TOKEN);
        }
        // 存放session
        SessionContext.setSession(userSession);
        return true;
    }

    private boolean apiAuth(String appId, String time, String sign) {
        //获取商户
        Merchant merchant = merchantService.findByAppId(appId);
        if(merchant == null){
            throw new GlobalException(ResultCode.APPID_ERROR);
        }
        String mySign = SignUtil.sign(appId + time, merchant.getSecretKey());
        if(!mySign.equalsIgnoreCase(sign)){
            throw new GlobalException(ResultCode.SIGN_ERROR);
        }
        APISession session = new APISession();
        session.setAppId(appId);
        session.setMerchant(merchant);
        APISessionContext.setSession(session);
        return true;
    }
}
