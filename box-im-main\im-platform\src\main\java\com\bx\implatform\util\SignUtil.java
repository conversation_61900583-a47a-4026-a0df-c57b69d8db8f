package com.bx.implatform.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

@Slf4j
public class SignUtil {

    public static String sign(String data, String secretKey){
        String sign = "";
        try{
            byte[] byteKey = secretKey.getBytes("UTF-8");
            Mac hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec keySpec = new SecretKeySpec(byteKey, "HmacSHA256");
            hmac.init(keySpec);
            byte[] byteSig = hmac.doFinal(data.getBytes("UTF-8"));
            sign = Hex.encodeHexString(byteSig);
        }catch (Exception e){
            log.error("sign error", e);
        }
        return sign;
    }

    public static void main(String[] args) {
        String data="10011756989109121";
        String secretKey="DIASDUAQAS";
        System.out.println(SignUtil.sign(data, secretKey));
    }
}
