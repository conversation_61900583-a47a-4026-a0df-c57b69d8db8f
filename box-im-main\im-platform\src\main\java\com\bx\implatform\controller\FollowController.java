package com.bx.implatform.controller;

import com.bx.implatform.annotation.RepeatSubmit;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.bx.implatform.service.FollowService;
import com.bx.implatform.session.SessionContext;
import com.bx.implatform.vo.FriendVO;
import com.bx.implatform.vo.RelationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "关注相关接口")
@RestController
@RequestMapping("/follow")
@RequiredArgsConstructor
public class FollowController {

    private final FollowService followService;

    @PostMapping("/list")
    @Operation(summary = "关注列表", description = "获取我关注的用户列表")
    public Result<List<RelationVO>> followings() {
        return ResultUtils.success(followService.followings(SessionContext.getSession().getUserName()));
    }

    @RepeatSubmit
    @PostMapping("/add")
    @Operation(summary = "关注", description = "关注指定用户")
    public Result add(@NotNull(message = "用户id不可为空") @RequestParam String userId) {
        followService.follow(SessionContext.getSession().getUserName(), userId);
        return ResultUtils.success();
    }

    @DeleteMapping("/remove")
    @Operation(summary = "取消关注", description = "取消关注指定用户")
    public Result remove(@NotNull(message = "用户id不可为空") @RequestParam String userId) {
        followService.unfollow(SessionContext.getSession().getUserName(), userId);
        return ResultUtils.success();
    }
}