package com.bx.implatform;

import com.bx.implatform.api.UserApiTest;

public class ContactsTest extends BaseTest{

    public static void test() {
        String token = UserApiTest.token("1").getAccessToken();
        login(token);
//        superior();
//        subordinateList();
        friendList();
//        followList();
    }

    public static void superior(){
        post("/user/superior");
    }

    public static void subordinateList(){
        post("/user/subordinate/list");
    }

    public static void friendList(){
        post("/friend/list");
    }

    public static void followList(){
        post("/follow/list");
    }
}
