package com.bx.implatform.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
@Schema(description = "关注")
public class ApiFollowDTO {

    @NotEmpty(message = "用户id不可为空")
    @Schema(description = "用户id")
    private String userId;

    @NotEmpty(message = "关注id不可为空")
    @Schema(description = "关注id")
    private String followId;
}
