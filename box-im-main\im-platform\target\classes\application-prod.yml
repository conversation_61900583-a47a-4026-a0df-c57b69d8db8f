spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************
    username: root
    password: naQRLdDPbGv2j7PX_
  data:
    redis:
      host: localhost
      port: 6379
      password: PmEpfRjpBnTN6CgW

minio:
  endpoint: http://127.0.0.1:9001 #内网地址
  domain: https://www.boxim.online/file  #外网访问地址
  accessKey: admin
  secretKey: 3fBSt6AkgFuD77D6
  bucketName: box-im
  imagePath: image
  filePath: file
  videoPath: video
  expireIn: 180 # 文件过期时间,单位:天

webrtc:
  max-channel: 9 # 多人通话最大通道数量，最大不能超过16,建议值:4,9,16
  iceServers:   #coturn配置
    - urls: stun:www.boxim.online:3478
      username: admin
      credential: UrHHKNvE7nFvBTMV
    - urls: turn:www.boxim.online:3478
      username: admin
      credential: UrHHKNvE7nFvBTMV

sms:
  platform: aliyun  # 目前仅支持阿里云短信
  access-key: LTAI5tEjGjBgjz5nTP4MkSvR
  secret-key: ******************************
  sign-name: xxx科技 # 短信签名,需在阿里云控制台审核通过
  template-id: SMS_481125065 # 短信模版id,验证码变量必须为: ${code}

mail:
  host: smtp.163.com
  port: 465
  ssl: true
  name: 广州联盒科技
  from: <EMAIL>  # 发送方邮箱
  pass: BWhJQ9xxx36qKhq # 授权码，邮箱后台开启授权后获得
  subject: '您收到一条注册验证码'
  content: '您正在注册盒子IM账号,验证码为:  ${code} ,有效期为5分钟'


registration:
  mode:
    - username #用户名注册
    - phone # 手机注册,需开启短信功能
    - email # 邮箱注册,需开启短信功能
