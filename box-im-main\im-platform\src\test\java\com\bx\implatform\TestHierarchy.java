package com.bx.implatform;

import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

public class TestHierarchy extends BaseTest{

    public static void bind(long userId, long superiorId){
        Map<String, Object> urlVariables = new HashMap<>();
        urlVariables.put("userId", userId);
        urlVariables.put("superiorId", superiorId);
//        ResponseEntity<String> response = restTemplate.postForEntity(getUrl("api/hierarchy/bind?userId={userId}&superiorId={superiorId}")
//                , null, String.class, urlVariables);
        ResponseEntity<String> response = restTemplate.postForEntity(getUrl("api/user/token?userId={userId}&superiorId={superiorId}")
                , null, String.class, urlVariables);
        System.out.println(response.getBody());
    }
}
