<template>
	<view @click="selectAndUpload()">
		<slot></slot>
	</view>
</template>

<script>
import UNI_APP from '@/.env.js'

export default {
	name: "image-upload",
	data() {
		return {}
	},
	props: {
		maxCount: {
			type: Number,
			default: 1
		},
		maxSize: {
			type: Number,
			default: 10
		},
		sourceType: {
			type: String,
			default: 'album'
		},
		isPermanent: {
			type: Boolean,
			default: false
		},
		thumbSize: {
			type: Number,
			default: 50
		},
		onBefore: {
			type: Function,
			default: null
		},
		onSuccess: {
			type: Function,
			default: null
		},
		onError: {
			type: Function,
			default: null
		}
	},
	methods: {
		async selectAndUpload() {
			if (this.sourceType == 'album' && !await this.$permission.storage()) {
				console.log("相册权限未获得")
				return
			}
			if (this.sourceType == 'camera' && !await this.$permission.camera()) {
				console.log("相机权限未获得")
				return
			}
			uni.chooseImage({
				count: this.maxCount, //最多可以选择的图片张数，默认9
				sourceType: [this.sourceType], //album 从相册选图，camera 使用相机，默认二者都有。如需直接开相机或直接选相册，请只使用一个选项
				sizeType: ['original'], //original 原图，compressed 压缩图，默认二者都有
				success: (res) => {
					res.tempFiles.forEach((file) => {
						if (file.size > this.maxSize * 1024 * 1024) {
							uni.showToast({
								title: `图片大小不得大于${this.maxSize}M`,
								icon: "none"
							})
							return;
						}
						if (!this.onBefore || this.onBefore(file)) {
							// 调用上传图片的接口
							this.uploadImage(file);
						}
					})
				}
			})
		},
		uploadImage(file) {
			let action = `/image/upload?isPermanent=${this.isPermanent}&thumbSize=${this.thumbSize}`
			uni.uploadFile({
				url: UNI_APP.BASE_URL + action,
				header: {
					accessToken: uni.getStorageSync("loginInfo").accessToken
				},
				filePath: file.path, // 要上传文件资源的路径
				name: 'file',
				success: (res) => {
					let data = JSON.parse(res.data);
					if (data.code != 200) {
						uni.showToast({
							icon: "none",
							title: data.message,
						})
						this.onError && this.onError(file, data);
					} else {
						this.onSuccess && this.onSuccess(file, data);
					}
				},
				fail: (err) => {
					console.log(err);
					this.onError && this.onError(file, err);
				}
			})
		}
	}
}
</script>

<style></style>