package com.bx.implatform;

import com.alibaba.fastjson.JSONObject;
import com.bx.implatform.api.vo.LoginTokenVO;
import com.bx.implatform.util.SignUtil;
import com.bx.implatform.vo.LoginVO;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class BaseTest {

    public static TestRestTemplate restTemplate = new TestRestTemplate();

//    public static String host = "https://platformapi.imlab.cc/";
public static String host = "http://localhost:8880/";

    public static String getUrl(String url){
        return host + url;
    }

    public static String accessToken;

    public static String refreshToken;

    public static void login(String token){
        accessToken = token;
        String data = post("/login", null);
        JSONObject obj = JSONObject.parseObject(data);
        LoginVO vo = JSONObject.parseObject(obj.getString("data"), LoginVO.class);
        accessToken = vo.getAccessToken();
        refreshToken = vo.getRefreshToken();
    }

    public static String send(String path, HttpMethod httpMethod, Object obj, Object... urlVariables){
        HttpHeaders headers = new HttpHeaders();
        headers.set("accessToken", accessToken);
        headers.set("content-type", "application/json");
        HttpEntity<String> entity = null;
        if(obj != null){
            entity = new HttpEntity<>(JSONObject.toJSONString(obj), headers);
        }else{
            entity = new HttpEntity<>(headers);
        }
        ResponseEntity<String> res = null;
        if(urlVariables != null){
            res = restTemplate.exchange(getUrl(path), httpMethod, entity, String.class, urlVariables);
        }else{
            res = restTemplate.exchange(getUrl(path), httpMethod, entity, String.class);
        }
        String body = res.getBody();
        System.out.println(body);
        return body;
    }

    public static String post(String path){
        return post(path, null);
    }

    public static String post(String path, Object obj, Object... urlVariables){
        return send(path, HttpMethod.POST, obj, urlVariables);
    }

    public static String get(String path, Object obj, Object... urlVariables){
        return send(path, HttpMethod.GET, obj, urlVariables);
    }

    public static String delete(String path, Object obj, Object... urlVariables){
        return send(path, HttpMethod.DELETE, obj, urlVariables);
    }

    public static String put(String path, Object obj, Object... urlVariables){
        return send(path, HttpMethod.PUT, obj, urlVariables);
    }

}
