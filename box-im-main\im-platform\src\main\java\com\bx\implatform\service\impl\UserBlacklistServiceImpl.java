package com.bx.implatform.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bx.imclient.IMClient;
import com.bx.implatform.contant.RedisKey;
import com.bx.implatform.entity.User;
import com.bx.implatform.entity.UserBlacklist;
import com.bx.implatform.exception.GlobalException;
import com.bx.implatform.mapper.UserBlacklistMapper;
import com.bx.implatform.mapper.UserMapper;
import com.bx.implatform.service.UserBlacklistService;
import com.bx.implatform.service.UserService;
import com.bx.implatform.session.SessionContext;
import com.bx.implatform.session.UserSession;
import com.bx.implatform.vo.RelationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: Blue
 * @date: 2024-09-22
 * @version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@CacheConfig(cacheNames = RedisKey.IM_CACHE_BLACKLIST)
public class UserBlacklistServiceImpl extends ServiceImpl<UserBlacklistMapper, UserBlacklist>
    implements UserBlacklistService {

    private final UserMapper userMapper;

    private final IMClient imClient;

    @CacheEvict(key = "#fromUserId+':'+#toUserId")
    @Override
    public void add(Long fromUserId, Long toUserId) {
        if (isInBlacklist(fromUserId, toUserId)) {
            throw new GlobalException("对方已在您的黑名单列表中");
        }
        UserBlacklist bl = new UserBlacklist();
        bl.setFromUserId(fromUserId);
        bl.setToUserId(toUserId);
        bl.setCreateTime(new Date());
        this.save(bl);
    }

    @CacheEvict(key = "#fromUserId+':'+#toUserId")
    @Override
    public void remove(Long fromUserId, Long toUserId) {
        UserSession session = SessionContext.getSession();
        LambdaQueryWrapper<UserBlacklist> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserBlacklist::getFromUserId, session.getUserId());
        wrapper.eq(UserBlacklist::getToUserId, toUserId);
        this.remove(wrapper);
    }

    @Cacheable(key = "#fromUserId+':'+#toUserId")
    @Override
    public Boolean isInBlacklist(Long fromUserId, Long toUserId) {
        LambdaQueryWrapper<UserBlacklist> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserBlacklist::getFromUserId, fromUserId);
        wrapper.eq(UserBlacklist::getToUserId, toUserId);
        return this.count(wrapper) > 0;
    }

    @Override
    public List<RelationVO> list(Long userId) {
        LambdaQueryWrapper<UserBlacklist> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserBlacklist::getFromUserId, userId);
        List<UserBlacklist> userBlacklists = this.list(wrapper);
        if(userBlacklists.isEmpty()){
            return List.of();
        }
        List<Long> ids = new ArrayList<>();
        for(UserBlacklist obj : userBlacklists){
            ids.add(obj.getToUserId());
        }
        List<User> users = userMapper.selectBatchIds(ids);
        List<Long> onlines = imClient.getOnlineUser(ids);
        return users.stream().map(u-> new RelationVO(u, onlines.contains(u.getId()))).toList();
    }

}
