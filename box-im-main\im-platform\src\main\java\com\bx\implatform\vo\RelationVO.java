package com.bx.implatform.vo;

import cn.hutool.core.collection.CollectionUtil;
import com.bx.imcommon.enums.IMTerminalType;
import com.bx.implatform.entity.Friend;
import com.bx.implatform.entity.User;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
@Schema(description = "关系信息VO")
public class RelationVO {

    @NotNull(message = "id不可为空")
    @Schema(description = "id")
    private Long id;

    @NotEmpty(message = "用户名不可为空")
    @Schema(description = "用户名")
    private String userName;

    @NotNull(message = "昵称不可为空")
    @Schema(description = "好友昵称")
    private String nickName;

    @Schema(description = "显示名称")
    private String showNickName;

    @Schema(description = "昵称备注")
    private String remarkNickName;

    @Schema(description = "头像")
    private String headImage;

    @Schema(description = "是否在线")
    private Boolean online;

    @Schema(description = "离线时间（毫秒时间戳）")
    private Long offlineTime;

    @Schema(description = "用户来源")
    private String source;

    @Schema(description = "vip等级")
    private Integer vip;

    public RelationVO(){}

    public RelationVO(User user, boolean online){
        this.id = user.getId();
        this.userName = user.getUserName();
        this.headImage = user.getHeadImage();
        this.nickName = user.getNickName();
        this.online = false;
        if (online) {
            this.online = true;
        }else{
            this.offlineTime = user.getOfflineTime();
        }
        this.source = user.getSource();
        this.vip = user.getVip();
    }

    public RelationVO(Friend friend, User user, List<IMTerminalType> terminalMap){
        if(user != null){
            this.id = user.getId();
            this.userName = user.getUserName();
            this.headImage = user.getHeadImage();
            this.nickName = user.getNickName();
        }else{
            this.id = friend.getFriendId();
//            this.userName = friend.getFriendUserName();
            this.headImage = friend.getFriendHeadImage();
            this.nickName = friend.getFriendNickName();
        }
        this.remarkNickName = friend.getRemarkNickName();
        this.showNickName = StringUtils.isEmpty(this.remarkNickName) ? this.nickName : this.remarkNickName;
        this.online = false;
        if (CollectionUtil.isNotEmpty(terminalMap)) {
            this.online = true;
        }else{
            if(user != null && user.getOfflineTime() != null){
                this.offlineTime = user.getOfflineTime();
            }
        }
        if(user != null){
            this.source = user.getSource();
            this.vip = user.getVip();
        }
    }

}
