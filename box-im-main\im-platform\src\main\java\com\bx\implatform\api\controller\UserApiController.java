package com.bx.implatform.api.controller;

import com.bx.implatform.api.dto.LoginTokenDTO;
import com.bx.implatform.api.service.UserApiService;
import com.bx.implatform.api.vo.LoginTokenVO;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.bx.implatform.vo.UserImportVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "服务器api-用户相关")
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class UserApiController {

    private final UserApiService userApiService;

    @PostMapping("/token")
    @Operation(summary = "获取token", description = "")
    public Result<LoginTokenVO> token(@RequestBody LoginTokenDTO tokenDTO){
        LoginTokenVO vo = userApiService.token(tokenDTO);
        return ResultUtils.success(vo);
    }

    @PostMapping("/import")
    @Operation(summary = "导入用户", description = "导入用户数据")
    public Result importData(@RequestBody List<UserImportVO> users){
        return ResultUtils.success(userApiService.importUser(users));
    }


}

