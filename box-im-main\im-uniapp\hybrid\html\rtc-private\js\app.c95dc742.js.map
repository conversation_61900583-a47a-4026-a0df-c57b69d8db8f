{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/view/ChatVideo.vue?05a4", "webpack:///./src/App.vue", "webpack:///./src/view/ChatVideo.vue", "webpack:///./src/common/request.js", "webpack:///./src/common/webrtc.js", "webpack:///./src/common/permission.js", "webpack:///./src/common/camera.js", "webpack:///./src/common/api.js", "webpack:///./src/common/uniEvent.js", "webpack:///./src/components/HeadImage.vue", "webpack:///src/components/HeadImage.vue", "webpack:///./src/components/HeadImage.vue?7a04", "webpack:///./src/components/HeadImage.vue?73d2", "webpack:///src/view/ChatVideo.vue", "webpack:///./src/view/ChatVideo.vue?fb2b", "webpack:///./src/view/ChatVideo.vue?e517", "webpack:///src/App.vue", "webpack:///./src/App.vue?03b3", "webpack:///./src/App.vue?315a", "webpack:///./src/common/enums.js", "webpack:///./src/main.js", "webpack:///./src/App.vue?7624", "webpack:///./src/assets/audio/handup.wav", "webpack:///./src/assets/audio/call.wav", "webpack:///./src/components/HeadImage.vue?03f1"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "render", "_vm", "this", "_c", "_self", "attrs", "staticRenderFns", "staticClass", "isJSBridgeReady", "style", "backgroundImage", "friend", "headImage", "ref", "require", "directives", "rawName", "isConnected", "isVoiceMode", "expression", "showNickName", "_v", "_s", "isVideoMode", "class", "isFacing", "domProps", "tip", "chatTimeString", "isMicroPhone", "on", "$event", "onSwitchMicroPhone", "isHost", "onReject", "onAccept", "onCancel", "onHandup", "onSwitchCamera", "isSpeaker", "onSwitchSpeaker", "createHttpRequest", "baseUrl", "loginInfo", "plus", "XMLHttpRequest", "net", "http", "axios", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "accessToken", "encodeURIComponent", "error", "Promise", "reject", "response", "async", "code", "console", "log", "refreshToken", "method", "url", "catch", "undefined", "ImWebRtc", "isEnable", "RTCPeerConnection", "webkitRTCPeerConnection", "mozRTCPeerConnection", "RTCSessionDescription", "webkitRTCSessionDescription", "mozRTCSessionDescription", "RTCIceCandidate", "webkitRTCIceCandidate", "mozRTCIceCandidate", "init", "configuration", "isAndroid11", "fixAndroid", "setupPeerConnection", "stream", "callback", "peerConnection", "ontrack", "e", "streams", "getTracks", "for<PERSON>ach", "track", "addTrack", "switchStream", "senders", "getSenders", "videoTrack", "getVideoTracks", "audioTrack", "getAudioTracks", "sender", "kind", "replaceTrack", "onIcecandidate", "onicecandidate", "event", "candidate", "onStateChange", "oniceconnectionstatechange", "state", "target", "iceConnectionState", "createOffer", "resolve", "offerParam", "then", "offer", "setLocalDescription", "createAnswer", "setRemoteDescription", "answer", "addIceCandidate", "close", "uid", "onaddstream", "deviceInfo", "navigator", "userAgent", "androidVersion", "match", "iceCandidatePoolSize", "peer", "offerToReceiveAudio", "offerToReceiveVideo", "setTimeout", "requestPermissions", "scope", "message", "android", "resultObj", "topMessageView", "createTopMessage", "title", "nativeObj", "View", "width", "height", "backgroundColor", "drawText", "top", "left", "color", "size", "whiteSpace", "show", "checkAndRequest", "messageTip", "settingTip", "os", "res", "clearTimeout", "granted", "storage", "camera", "micro", "ImCamera", "constructor", "mediaDevices", "getUserMedia", "openVideo", "permission", "ratio", "devicePixelRatio", "facingMode", "constraints", "video", "screen", "audio", "echoCancellation", "noiseSuppression", "openAudio", "stop", "ImA<PERSON>", "setup", "accept", "handup", "cancel", "failed", "reason", "JSON", "stringify", "sendCandidate", "heartbeat", "UniEvent", "listen", "onEvent", "parse", "decodeURIComponent", "addEventListener", "isReady", "avatarTextStyle", "substring", "toUpperCase", "_t", "colors", "props", "id", "type", "Number", "default", "String", "Boolean", "methods", "computed", "textColor", "hash", "charCodeAt", "component", "env", "webrtc", "uniEvent", "API", "localStream", "remoteStream", "isLock", "candidates", "chatTime", "audioPlayer", "disconnectTimer", "chatTimer", "heartbeatTimer", "userId", "components", "HeadImage", "enabled", "cameraName", "$refs", "mineVideo", "srcObject", "play", "document", "getElementById", "pause", "muted", "onCall", "callAudio", "tryLock", "checkDevEnable", "initRtc", "onConnected", "onDisconnected", "onNavBack", "onRTCSetup", "msg", "onRTCAccept", "selfSend", "onRTCRejct", "onRTCCancel", "onRTCFailed", "content", "onRTCHandup", "onRTCOffer", "startChatTime", "onRTCAnswer", "onRTCCandidate", "onRTCMessage", "sendId", "$enums", "MESSAGE_TYPE", "RTC_SETUP_VIDEO", "RTC_SETUP_VOICE", "RTC_ACCEPT", "RTC_REJECT", "RTC_CANCEL", "RTC_HANDUP", "RTC_FAILED", "RTC_OFFER", "RTC_ANSWER", "RTC_CANDIDATE", "clearInterval", "setInterval", "startHeartBeat", "initEvent", "connected", "<PERSON><PERSON><PERSON><PERSON>", "refreshTitle", "strTitle", "iceServers", "handupAudio", "uni", "postMessage", "decodeURL", "val", "min", "Math", "floor", "sec", "strTime", "mounted", "URL", "location", "href", "searchParams", "getEnv", "ChatVideo", "<PERSON><PERSON>", "enums", "productionTip", "h", "App", "$mount"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAKnBhB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASS,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAU6B,QAGnC,IAAIC,EAASF,EAAiB5B,GAAY,CACzCK,EAAGL,EACH+B,GAAG,EACHF,QAAS,IAUV,OANAf,EAAQd,GAAUW,KAAKmB,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOC,GAAI,EAGJD,EAAOD,QAKfH,EAAoBM,EAAIlB,EAGxBY,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,SAASL,EAASM,EAAMC,GAC3CV,EAAoBW,EAAER,EAASM,IAClC3B,OAAO8B,eAAeT,EAASM,EAAM,CAAEI,YAAY,EAAMC,IAAKJ,KAKhEV,EAAoBe,EAAI,SAASZ,GACX,qBAAXa,QAA0BA,OAAOC,aAC1CnC,OAAO8B,eAAeT,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DpC,OAAO8B,eAAeT,EAAS,aAAc,CAAEe,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKxC,OAAOyC,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBxC,OAAO8B,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBQ,EAAEc,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAStB,GAChC,IAAIM,EAASN,GAAUA,EAAOiB,WAC7B,WAAwB,OAAOjB,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAJ,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASgB,EAAQC,GAAY,OAAO9C,OAAOC,UAAUC,eAAeC,KAAK0C,EAAQC,IAGzG5B,EAAoB6B,EAAI,GAExB,IAAIC,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAW3C,KAAKsC,KAAKK,GAC5CA,EAAW3C,KAAOf,EAClB0D,EAAaA,EAAWG,QACxB,IAAI,IAAItD,EAAI,EAAGA,EAAImD,EAAWjD,OAAQF,IAAKP,EAAqB0D,EAAWnD,IAC3E,IAAIU,EAAsB2C,EAI1BzC,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,6ECvJT,Y,2KCAIyC,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,eAAe,IAEhHG,EAAkB,GCFlBN,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACI,YAAY,cAAc,CAAGN,EAAIO,gBAAgDL,EAAG,MAAM,CAACI,YAAY,OAAOE,MAAO,CAAEC,gBAAiB,OAAST,EAAIU,OAAOC,UAAY,OAAtHT,EAAG,MAAM,CAACI,YAAY,SAAyGJ,EAAG,QAAQ,CAACU,IAAI,YAAYR,MAAM,CAAC,KAAO,SAAS,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,IAAMS,EAAQ,aAAgCX,EAAG,QAAQ,CAACU,IAAI,cAAcR,MAAM,CAAC,iBAAiB,GAAG,YAAc,GAAG,qBAAqB,KAAK,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,IAAMS,EAAQ,aAAkCX,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,OAAQiB,EAAIgB,aAAahB,EAAIiB,YAAaC,WAAW,8BAA8BZ,YAAY,iBAAiB,CAACJ,EAAG,aAAa,CAACE,MAAM,CAAC,QAAUJ,EAAIO,gBAAgB,IAAMP,EAAIU,OAAOC,UAAU,KAAOX,EAAIU,OAAOS,aAAa,KAAO,MAAM,CAACjB,EAAG,MAAM,CAACI,YAAY,eAAe,CAACN,EAAIoB,GAAGpB,EAAIqB,GAAGrB,EAAIU,OAAOS,oBAAoB,GAAGjB,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,MAAOiB,EAAIgB,aAAahB,EAAIsB,YAAaJ,WAAW,6BAA6BZ,YAAY,aAAa,CAACJ,EAAG,MAAM,CAACI,YAAY,gBAAgB,CAACJ,EAAG,QAAQ,CAACU,IAAI,cAAcN,YAAY,qBAAqBF,MAAM,CAAC,GAAK,cAAc,6BAA6B,OAAO,iBAAiB,GAAG,YAAc,GAAG,qBAAqB,QAAQF,EAAG,MAAM,CAACI,YAAY,cAAc,CAACJ,EAAG,QAAQ,CAACU,IAAI,YAAYN,YAAY,mBAAmBiB,MAAMvB,EAAIwB,SAAS,UAAU,GAAGpB,MAAM,CAAC,GAAK,YAAY,MAAQ,GAAG,6BAA6B,OAAO,iBAAiB,GAAG,YAAc,GAAG,qBAAqB,IAAIqB,SAAS,CAAC,OAAQ,SAAYvB,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,MAAOiB,EAAI0B,IAAKR,WAAW,QAAQZ,YAAY,OAAO,CAACJ,EAAG,OAAO,CAACI,YAAY,YAAY,CAACN,EAAIoB,GAAGpB,EAAIqB,GAAGrB,EAAI0B,UAAUxB,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,MAAOiB,EAAIgB,YAAaE,WAAW,gBAAgBZ,YAAY,aAAa,CAACJ,EAAG,OAAO,CAACI,YAAY,kBAAkB,CAACN,EAAIoB,GAAGpB,EAAIqB,GAAGrB,EAAI2B,qBAAqBzB,EAAG,MAAM,CAACI,YAAY,eAAe,CAACJ,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,MAAOiB,EAAIgB,aAAehB,EAAI4B,aAAcV,WAAW,gCAAgCZ,YAAY,6CAA6CuB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO9B,EAAI+B,yBAAyB7B,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,MAAOiB,EAAIgB,cAAgBhB,EAAI4B,aAAcV,WAAW,iCAAiCZ,YAAY,8CAA8CuB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO9B,EAAI+B,yBAAyB7B,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,OAAQiB,EAAIgC,SAAWhC,EAAIgB,YAAaE,WAAW,4BAA4BZ,YAAY,+CAA+CuB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO9B,EAAIiC,eAAe/B,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,OAAQiB,EAAIgC,SAAWhC,EAAIgB,YAAaE,WAAW,4BAA4BZ,YAAY,2CAA2CuB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO9B,EAAIkC,eAAehC,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,MAAOiB,EAAIgC,SAAWhC,EAAIgB,YAAaE,WAAW,2BAA2BZ,YAAY,+CAA+CuB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO9B,EAAImC,eAAejC,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,MAAOiB,EAAIgB,YAAaE,WAAW,gBAAgBZ,YAAY,wDAAwDuB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO9B,EAAIoC,eAAelC,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,MAAOiB,EAAIgB,aAAehB,EAAIsB,aAAetB,EAAIwB,SAAUN,WAAW,2CAA2CZ,YAAY,4CAA4CuB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO9B,EAAIqC,qBAAqBnC,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,MAAOiB,EAAIgB,aAAehB,EAAIsB,cAAgBtB,EAAIwB,SAAUN,WAAW,4CAA4CZ,YAAY,2CAA2CuB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO9B,EAAIqC,qBAAqBnC,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,MAAOiB,EAAIgB,aAAehB,EAAIiB,aAAejB,EAAIsC,UAAWpB,WAAW,4CAA4CZ,YAAY,0CAA0CuB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO9B,EAAIuC,sBAAsBrC,EAAG,MAAM,CAACY,WAAW,CAAC,CAACxC,KAAK,OAAOyC,QAAQ,SAAShC,MAAOiB,EAAIgB,aAAehB,EAAIiB,cAAgBjB,EAAIsC,UAAWpB,WAAW,6CAA6CZ,YAAY,2CAA2CuB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO9B,EAAIuC,2BAE7/IlC,EAAkB,G,0ECAtB,IAAImC,EAAoB,SAASC,EAASC,GACrC9C,OAAO+C,OAEV/C,OAAOgD,eAAiBhD,OAAO+C,KAAKE,IAAID,gBAGzC,MAAME,EAAOC,OAAM3D,OAAO,CACzB4D,QAASP,EACTQ,QAAS,IACTC,QAAS,CACR,eAAgB,qCAiDlB,OA1CAJ,EAAKK,aAAaC,QAAQC,IAAIC,IAC7B,IAAIC,EAAcb,EAAUa,YAI5B,OAHIA,IACHD,EAAOJ,QAAQK,YAAcC,mBAAmBD,IAE1CD,GACLG,GACKC,QAAQC,OAAOF,IAMvBX,EAAKK,aAAaS,SAASP,IAAIQ,UAC9B,GAA0B,KAAtBD,EAAS1H,KAAK4H,KACjB,OAAOF,EAAS1H,KAAKA,KACf,GAA0B,KAAtB0H,EAAS1H,KAAK4H,KAAa,CACrCC,QAAQC,IAAI,kBACZ,IAAIC,EAAevB,EAAUuB,aAgB7B,OAdAvB,QAAkBI,EAAK,CACtBoB,OAAQ,MACRC,IAAK,gBACLjB,QAAS,CACRe,aAAcA,KAEbG,MAAM,KACRL,QAAQC,IAAI,aAGsB,iBAAxBJ,EAASN,OAAOpH,OAC1B0H,EAASN,OAAOJ,aAAUmB,GAGpBvB,EAAKc,EAASN,QAErB,OAAOI,QAAQC,OAAOC,EAAS1H,OAE9BuH,IACFM,QAAQC,IAAI,kBACLN,QAAQC,OAAOF,KAEhBX,GAION,QC/Df,MAAM8B,GAENA,EAAS1H,UAAU2H,SAAW,WAO7B,OANA3E,OAAO4E,kBAAoB5E,OAAO4E,mBAAqB5E,OAAO6E,yBAA2B7E,OACvF8E,qBACF9E,OAAO+E,sBAAwB/E,OAAO+E,uBAAyB/E,OAAOgF,6BAA+BhF,OACnGiF,yBACFjF,OAAOkF,gBAAkBlF,OAAOkF,iBAAmBlF,OAAOmF,uBAAyBnF,OACjFoF,qBACOpF,OAAO4E,mBAGjBF,EAAS1H,UAAUqI,KAAO,SAASC,GAClCjF,KAAKiF,cAAgBA,EAGlBjF,KAAKkF,eACPlF,KAAKmF,cAIPd,EAAS1H,UAAUyI,oBAAsB,SAASC,EAAQC,GACzDtF,KAAKuF,eAAiB,IAAIhB,kBAAkBvE,KAAKiF,eACjDjF,KAAKuF,eAAeC,QAAWC,IAE9BH,EAASG,EAAEC,QAAQ,KAGhBL,GAEHA,EAAOM,YAAYC,QAASC,IAC3B7F,KAAKuF,eAAeO,SAASD,EAAOR,MAKvChB,EAAS1H,UAAUoJ,aAAe,SAASV,GAC1C,IAAIW,EAAUhG,KAAKuF,eAAeU,aAC9BC,EAAab,EAAOc,iBAAiB,GACrCC,EAAaf,EAAOgB,iBAAiB,GACzCL,EAAQJ,QAASU,IACZA,EAAOT,OAA8B,SAArBS,EAAOT,MAAMU,MAChCD,EAAOE,aAAaN,GAEjBI,EAAOT,OAA8B,SAArBS,EAAOT,MAAMU,MAChCD,EAAOE,aAAaJ,MAKvB/B,EAAS1H,UAAU8J,eAAiB,SAASnB,GAC5CtF,KAAKuF,eAAemB,eAAkBC,IAEjCA,EAAMC,WACTtB,EAASqB,EAAMC,aAKlBvC,EAAS1H,UAAUkK,cAAgB,SAASvB,GAE3CtF,KAAKuF,eAAeuB,2BAA8BH,IACjD,IAAII,EAAQJ,EAAMK,OAAOC,mBACzBnD,QAAQC,IAAI,gBAAkBgD,GAC9BzB,EAASyB,KAIX1C,EAAS1H,UAAUuK,YAAc,WAChC,OAAO,IAAIzD,QAAQ,CAAC0D,EAASzD,KAC5B,MAAM0D,EAAa,CACnBA,oBAAiC,EACjCA,oBAAiC,GAEjCpH,KAAKuF,eAAe2B,YAAYE,GAAYC,KAAMC,IAEjDtH,KAAKuF,eAAegC,oBAAoBD,GAExCH,EAAQG,KACNnD,MAAOsB,IACT/B,EAAO+B,QAMVpB,EAAS1H,UAAU6K,aAAe,SAASF,GAC1C,OAAO,IAAI7D,QAAQ,CAAC0D,EAASzD,KAE5B1D,KAAKyH,qBAAqBH,GAE1B,MAAMF,EAAa,CACnBA,oBAAiC,EACjCA,oBAAiC,GACjCpH,KAAKuF,eAAeiC,aAAaJ,GAAYC,KAAMK,IAElD1H,KAAKuF,eAAegC,oBAAoBG,GAExCP,EAAQO,KACNvD,MAAOsB,IACT/B,EAAO+B,QAMVpB,EAAS1H,UAAU8K,qBAAuB,SAASH,GAElDtH,KAAKuF,eAAekC,qBAAqB,IAAI/C,sBAAsB4C,KAGpEjD,EAAS1H,UAAUgL,gBAAkB,SAASf,GAE7C5G,KAAKuF,eAAeoC,gBAAgB,IAAI9C,gBAAgB+B,KAGzDvC,EAAS1H,UAAUiL,MAAQ,SAASC,GAE/B7H,KAAKuF,iBACRvF,KAAKuF,eAAeqC,QACpB5H,KAAKuF,eAAemB,eAAiB,KACrC1G,KAAKuF,eAAeuC,YAAc,OAKpCzD,EAAS1H,UAAUuI,YAAc,WAChC,GAAIvF,OAAO+C,KAAM,CAChB,MAAMqF,EAAaC,UAAUC,UACvBC,EAAiBH,EAAWI,MAAM,oBACxC,GAAID,GAA4C,IAA1BA,EAAezL,OAEpC,OADAqH,QAAQC,IAAI,kBAAkBmE,GACJ,MAAnBA,EAAe,GAGxB,OAAO,GAGR7D,EAAS1H,UAAUwI,WAAa,WAC/BrB,QAAQC,IAAI,oBACZ/D,KAAKiF,cAAcmD,qBAAuB,EAC1C,IAAIC,EAAO,IAAI9D,kBAAkBvE,KAAKiF,eACtCoD,EAAKnB,YAAY,CAChBoB,qBAAqB,EACrBC,qBAAqB,IACnBlB,KAAMC,IACRe,EAAKd,oBAAoBD,GACzBkB,WAAW,KACVH,EAAKT,QACL9D,QAAQC,IAAI,qBACV,QAKUM,QC7Jf,MAAMoE,EAAqBA,CAACC,EAAOC,IAC3B,IAAIlF,QAAQ,CAAC0D,EAASzD,KAC5B/D,OAAO+C,KAAKkG,QAAQH,mBACnB,CAAC,sBAAwBC,IACzB,SAASG,GACR/E,QAAQC,IAAI8E,EAAW,aACvB1B,EAAQ0B,MAGT,SAASrF,GACRE,SAQJ,IAAIoF,EACJ,MAAMC,EAAmBA,CAACC,EAAOL,KAChCG,EAAiB,IAAInJ,OAAO+C,KAAKuG,UAAUC,KAAK,iBAAkB,CACjEC,MAAO,OACPC,OAAQ,OACRC,gBAAiB,qBAElBP,EAAeQ,SACdN,EAAO,CACNO,IAAK,QACLC,KAAM,MACNL,MAAO,MACPC,OAAQ,OACN,CACFK,MAAO,UACPC,KAAM,SAGRZ,EAAeQ,SACdX,EAAS,CACRY,IAAK,MACLC,KAAM,MACNL,MAAO,MACPC,OAAQ,OACN,CACFK,MAAO,UACPE,WAAY,WAIdb,EAAec,QAGVC,EAAkBjG,MAAO8E,EAAOM,EAAOc,EAAYC,KACxD,GAAIpK,OAAO+C,MAAgC,QAAxB/C,OAAO+C,KAAKsH,GAAG3L,KAAgB,CACjD,IAAIU,EAAGyJ,WAAW,IAAMO,EAAiBC,EAAOc,GAAa,KACzDG,QAAYxB,EAAmBC,EAAOqB,GAI1C,GAHAG,aAAanL,GACb+J,GAAkBA,EAAelB,QACjC9D,QAAQC,IAAI,OAAQkG,IACfA,EAAIE,QAAQ,GAEhB,OAAO,EAGT,OAAO,GAGFC,EAAUxG,UACf,MAAM8E,EAAQ,yBACRM,EAAQ,cACRc,EAAa,yBACbC,EAAa,kDACnB,OAAOF,EAAgBnB,EAAOM,EAAOc,EAAYC,IAG5CM,EAASzG,UACd,MAAM8E,EAAQ,SACRM,EAAQ,WACRc,EAAa,kBACbC,EAAa,wCACnB,OAAOF,EAAgBnB,EAAOM,EAAOc,EAAYC,IAI5CO,EAAQ1G,UACb,MAAM8E,EAAQ,eACRM,EAAQ,YACRc,EAAa,yBACbC,EAAa,kDACnB,OAAOF,EAAgBnB,EAAOM,EAAOc,EAAYC,IAGnC,OACdK,UACAC,SACAC,SC5FD,MAAMC,EACLC,cACCxK,KAAKqF,OAAS,MAIhBkF,EAAS5N,UAAU2H,SAAW,WAC7B,QAAS0D,aAAeA,UAAUyC,gBAAkBzC,UAAUyC,aAAaC,cAG5EH,EAAS5N,UAAUgO,UAAY/G,eAAerC,GAI7C,OAHIvB,KAAKqF,QACRrF,KAAK4H,QAEC,IAAInE,QAAQ,CAAC0D,EAASzD,KAC5B8E,WAAW5E,UACV,UAAWgH,EAAWP,SACrB,OAAO3G,EAAO,CACbiF,QAAS,gBAGX,UAAWiC,EAAWN,QACrB,OAAO5G,EAAO,CACbiF,QAAS,cAGX,IAAIkC,EAAQlL,OAAOmL,kBAAoB,EACnCC,EAAaxJ,EAAW,OAAS,cACjCyJ,EAAc,CACjBC,MAAO,CAEN9B,MAAOxJ,OAAOuL,OAAO9B,OAASyB,EAC9BzB,OAAQzJ,OAAOuL,OAAO/B,MAAQ0B,EAC9BE,WAAYA,GAEbI,MAAO,CACNC,kBAAkB,EAClBC,kBAAkB,IAGpBrD,UAAUyC,aAAaC,aAAaM,GAAa3D,KAAMhC,IACtDvB,QAAQC,IAAI,SACZ/D,KAAKqF,OAASA,EACd8B,EAAQ9B,KACNlB,MAAOsB,IACT3B,QAAQC,IAAI,YAAa0B,GACzB/B,EAAO,CACNG,KAAM,EACN8E,QAAS,qBAOd4B,EAAS5N,UAAU2O,UAAY,WAI9B,OAHItL,KAAKqF,QACRrF,KAAK4H,QAEC,IAAInE,QAAQ,CAAC0D,EAASzD,KAC5B8E,WAAW5E,UACV,UAAWgH,EAAWN,QACrB,OAAO5G,EAAO,CACbG,KAAM,EACN8E,QAAS,cAGX,IAAIqC,EAAc,CACjBC,OAAO,EACPE,MAAO,CACNC,kBAAkB,EAClBC,kBAAkB,IAGpBrD,UAAUyC,aAAaC,aAAaM,GAAa3D,KAAMhC,IACtDrF,KAAKqF,OAASA,EACd8B,EAAQ9B,KACNlB,MAAM,KACRL,QAAQC,IAAI,aACZL,EAAO,CACNG,KAAM,EACN8E,QAAS,qBAOd4B,EAAS5N,UAAUiL,MAAQ,WAEtB5H,KAAKqF,SACRrF,KAAKqF,OAAOM,YAAYC,QAASC,IAChCA,EAAM0F,SAEPvL,KAAKqF,OAAS,OAIDkF,QClGf,MAAMiB,EACLhB,YAAYhI,EAASC,GACpBzC,KAAK6C,KAAON,EAAkBC,EAASC,IAKzC+I,EAAM7O,UAAU8O,MAAQ,SAAS5D,EAAK7I,GACrC,OAAOgB,KAAK6C,KAAK,CAChBqB,IAAK,6BAA6B2D,UAAY7I,IAC9CiF,OAAQ,UAIVuH,EAAM7O,UAAU+O,OAAS,SAAS7D,GACjC,OAAO7H,KAAK6C,KAAK,CAChBqB,IAAK,8BAA8B2D,EACnC5D,OAAQ,UAKVuH,EAAM7O,UAAUgP,OAAS,SAAS9D,GACjC,OAAO7H,KAAK6C,KAAK,CAChBqB,IAAK,8BAA8B2D,EACnC5D,OAAQ,UAIVuH,EAAM7O,UAAUiP,OAAS,SAAS/D,GACjC,OAAO7H,KAAK6C,KAAK,CAChBqB,IAAK,8BAA8B2D,EACnC5D,OAAQ,UAIVuH,EAAM7O,UAAU+G,OAAS,SAASmE,GACjC,OAAO7H,KAAK6C,KAAK,CAChBqB,IAAK,8BAA8B2D,EACnC5D,OAAQ,UAIVuH,EAAM7O,UAAUkP,OAAS,SAAShE,EAAKiE,GACtC,OAAO9L,KAAK6C,KAAK,CAChBqB,IAAK,8BAA8B2D,YAAciE,IACjD7H,OAAQ,UAIVuH,EAAM7O,UAAU2K,MAAQ,SAASO,EAAKP,GACrC,OAAOtH,KAAK6C,KAAK,CAChBqB,IAAK,6BAA6B2D,EAClC5D,OAAQ,OACRhI,KAAM8P,KAAKC,UAAU1E,MAIvBkE,EAAM7O,UAAU+K,OAAS,SAASG,EAAKH,GACtC,OAAO1H,KAAK6C,KAAK,CAChBqB,IAAK,8BAA8B2D,EACnC5D,OAAQ,OACRhI,KAAM8P,KAAKC,UAAUtE,MAIvB8D,EAAM7O,UAAUsP,cAAgB,SAASpE,EAAKjB,GAC7C,OAAO5G,KAAK6C,KAAK,CAChBqB,IAAK,iCAAiC2D,EACtC5D,OAAQ,OACRhI,KAAM8P,KAAKC,UAAUpF,MAIvB4E,EAAM7O,UAAUuP,UAAY,SAASrE,GACpC,OAAO7H,KAAK6C,KAAK,CAChBqB,IAAK,iCAAiC2D,EACtC5D,OAAQ,UAIKuH,QC/Ef,MAAMW,GAENA,EAASxP,UAAUyP,OAAS,SAAS9G,GAEpC3F,OAAO0M,QAAW5G,IACjB,IAAIkB,EAAQoF,KAAKO,MAAMC,mBAAmB9G,IAC1CH,EAASqB,EAAMvH,IAAIuH,EAAM1K,OAG1B0D,OAAO6M,iBAAiB,WAAW,SAAU/G,GAC5C,MAAMkB,EAAQlB,EAAExJ,KAChBqJ,EAASqB,EAAMvH,IAAIuH,EAAM1K,SACxB,IAIYkQ,QCpBXrM,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACI,YAAY,cAAc,CAAEN,EAAI0M,SAAS1M,EAAImE,IAAKjE,EAAG,MAAM,CAACI,YAAY,eAAeF,MAAM,CAAC,IAAMJ,EAAImE,OAAOjE,EAAG,MAAM,CAACI,YAAY,cAAcE,MAAOR,EAAI2M,iBAAkB,CAAC3M,EAAIoB,GAAG,IAAIpB,EAAIqB,GAAGrB,EAAI1B,KAAKsO,UAAU,EAAE,GAAGC,eAAe,OAAO7M,EAAI8M,GAAG,YAAY,IAE7UzM,EAAkB,GCSP,GACf/B,KAAA,YACApC,OACA,OACA6Q,OAAA,mDACA,qDAIAC,MAAA,CACAC,GAAA,CACAC,KAAAC,QAEAxD,KAAA,CACAuD,KAAAC,OACAC,QAAA,IAEAjJ,IAAA,CACA+I,KAAAG,QAEA/O,KAAA,CACA4O,KAAAG,OACAD,QAAA,KAEAV,QAAA,CACAQ,KAAAI,QACAF,SAAA,IAGAG,QAAA,GACAC,SAAA,CACAb,kBACA,oBAAAc,cAEAA,YACA,IAAAC,EAAA,EACA,QAAAlR,EAAA,EAAAA,EAAA,KAAA8B,KAAA5B,OAAAF,IACAkR,GAAA,KAAApP,KAAAqP,WAAAnR,GAEA,YAAAuQ,OAAAW,EAAA,KAAAX,OAAArQ,WClDkV,I,wBCQ9UkR,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QC8CA,GACf1R,OACA,OACA2R,IAAA,GACAtN,iBAAA,EACAtB,KAAA,QACAqL,OAAA,IAAAE,EACAsD,OAAA,IAAAxJ,EACAyJ,SAAA,IAAA3B,EACA4B,IAAA,KACAC,YAAA,KACAC,aAAA,KACAlM,QAAA,EACAJ,cAAA,EACAJ,UAAA,EACAc,WAAA,EACA6L,QAAA,EACAzM,IAAA,GACAsF,MAAA,OACAoH,WAAA,GACAC,SAAA,EACAC,YAAA,KACAC,gBAAA,KACAC,UAAA,KACAC,eAAA,KACAhM,QAAA,GACAC,UAAA,GACAgM,OAAA,KACAhO,OAAA,GACA4C,OAAA,KAGAqL,WAAA,CACAC,aAEArB,QAAA,CACAxL,qBACA,KAAAH,cAAA,KAAAA,aACA,KAAAqM,aACA,KAAAA,YAAArI,YAAAC,QAAAC,IACA,UAAAA,EAAAU,OACAV,EAAA+I,QAAA,KAAAjN,gBAIAmC,QAAAC,IAAA,YAAApC,eAEAS,iBACA,KAAAb,UAAA,KAAAA,SACA,KAAA8I,OAAAM,UAAA,KAAApJ,UAAA8F,KAAA2G,IACA,KAAAA,cAEA,KAAArM,cACA,KAAAqM,YAAArI,YAAAC,QAAAC,IACA,UAAAA,EAAAU,OACAV,EAAA+I,SAAA,KAKA,KAAAf,OAAA9H,aAAAiI,GACA,IAAAa,EAAA,KAAAtN,SAAA,UAEA,KAAAuN,MAAAC,UAAAC,UAAAhB,EACA,KAAAc,MAAAC,UAAAE,OAAA9K,MAAA,KACAL,QAAAC,IAAA,cAEAD,QAAAC,IAAA,SAAA8K,KACA1K,MAAAsB,IACA3B,QAAAC,IAAA,WAAA0B,EAAAkD,YAGArG,kBACA,KAAAD,WAAA,KAAAA,UACA,MAAA4I,EAAAiE,SAAAC,eAAA,eACAlE,EAAAmE,QACAnE,EAAAoE,OAAA,KAAAhN,UACA4I,EAAAgE,OAaAnL,QAAAC,IAAA,cAAA1B,YAEAiN,SAEA,KAAAvB,IAAAtC,MAAA,KAAAhL,OAAAuM,GAAA,KAAAhO,MAAAqI,KAAA,KACA,KAAA5F,IAAA,cACA,KAAAsF,MAAA,UACA,KAAA+H,MAAAS,UAAAN,OAAA9K,MAAAsB,IACA3B,QAAAC,IAAA,gBAEAI,MAAAsB,IACA,KAAAmC,MAAAnC,EAAAkD,YAGA1G,WAEA,YAAAuN,WAKA,KAAAV,MAAAS,UAAAH,QAEA,KAAAK,sBAKA,KAAAC,QAAA,KAEA,KAAA3B,IAAArC,OAAA,KAAAjL,OAAAuM,IAAA7I,MAAAsB,IACA,KAAAhE,IAAAgE,EAAAkD,aAPA,KAAAoF,IAAAlC,OAAA,KAAApL,OAAAuM,GAAA,cACA,KARAlJ,QAAAC,IAAA,eACA,IAiBA/B,WACA,SAAAwN,UAEA,OADA1L,QAAAC,IAAA,eACA,EAGA,KAAAgK,IAAArK,OAAA,KAAAjD,OAAAuM,IACA,KAAApF,MAAA,WAEA1F,WACA,SAAAsN,UAEA,OADA1L,QAAAC,IAAA,eACA,EAGA,KAAAgK,IAAAnC,OAAA,KAAAnL,OAAAuM,IACA,KAAApF,MAAA,WAEAzF,WAEA,KAAA4L,IAAApC,OAAA,KAAAlL,OAAAuM,IACA,KAAApF,MAAA,cAEA+H,cACA7L,QAAAC,IAAA,cACA,KAAAtC,IAAA,GACA,KAAA6M,iBAAApE,aAAA,KAAAoE,kBAEAsB,iBACA9L,QAAAC,IAAA,cACA,KAAAtC,IAAA,WAEA,KAAA6M,gBAAA9F,WAAA,KACA,KAAAZ,MAAA,cACA,OAEAiI,YACA/L,QAAAC,IAAA,QACA,KAAAhC,QAAA,gBAAAgF,OAEAjD,QAAAC,IAAA,UACA,KAAAgK,IAAAnC,OAAA,KAAAnL,OAAAuM,KACA,KAAAjL,QAAA,gBAAAgF,MAIA,gBAAAA,OAEA,KAAAgH,IAAApC,OAAA,KAAAlL,OAAAuM,KAJAlJ,QAAAC,IAAA,UACA,KAAAgK,IAAArK,OAAA,KAAAjD,OAAAuM,MAMA8C,WAAAC,GAEA,KAAAhJ,MAAA,UACA,KAAAtF,IAAA,WAAAJ,YAAA,iBACA,KAAAyN,MAAAS,UAAAN,OAAA9K,MAAAsB,IACA3B,QAAAC,IAAA,eAGAiM,YAAAD,GAEAA,EAAAE,SAEA,KAAArI,MAAA,YAGA,KAAAiG,OAAA3G,cAAAG,KAAAC,IACA,KAAAwH,MAAAS,UAAAH,QAEA,KAAArB,IAAAzG,MAAA,KAAA7G,OAAAuM,GAAA1F,MAIA4I,WAAAH,GACAA,EAAAE,SAEA,KAAArI,MAAA,cAGA,KAAAA,MAAA,gBAGAuI,YAAAJ,GACA,KAAAnI,MAAA,YAEAwI,YAAAL,GACAA,EAAAE,SACA,KAAArI,MAAA,QAEA,KAAAA,MAAAmI,EAAAM,UAIAC,YAAAP,GACA,KAAAnI,MAAA,uBAEA2I,WAAAR,GACA,MAAAzI,EAAAyE,KAAAO,MAAAyD,EAAAM,SACA,KAAAxC,OAAArG,aAAAF,GAAAD,KAAAK,IAEA,KAAAqG,IAAArG,OAAA,KAAAjH,OAAAuM,GAAAtF,KAGA,KAAAX,MAAA,UAEA,KAAAyJ,iBAEAC,YAAAV,GACA,MAAArI,EAAAqE,KAAAO,MAAAyD,EAAAM,SACA,KAAAxC,OAAApG,qBAAAC,GAEA,KAAAX,MAAA,UAEA,KAAAkF,gBAEA,KAAAuE,iBAEAE,eAAAX,GACA,KAAAlC,OAAAlG,gBAAAoE,KAAAO,MAAAyD,EAAAM,WAEAM,aAAAZ,GAEA,GAAAA,EAAAa,QAAA,KAAAnQ,OAAAuM,IACA+C,EAAAa,QAAA,KAAAnC,QACAsB,EAAA9C,MAAA,KAAA4D,OAAAC,aAAAC,iBACAhB,EAAA9C,MAAA,KAAA4D,OAAAC,aAAAE,gBAIA,OAAAjB,EAAA9C,MACA,UAAA4D,OAAAC,aAAAC,gBACA,KAAAjB,WAAAC,GACA,MACA,UAAAc,OAAAC,aAAAE,gBACA,KAAAlB,WAAAC,GACA,MACA,UAAAc,OAAAC,aAAAG,WACA,KAAAjB,YAAAD,GACA,MACA,UAAAc,OAAAC,aAAAI,WACA,KAAAhB,WAAAH,GACA,MACA,UAAAc,OAAAC,aAAAK,WACA,KAAAhB,YAAAJ,GACA,MACA,UAAAc,OAAAC,aAAAM,WACA,KAAAd,YAAAP,GACA,MACA,UAAAc,OAAAC,aAAAO,WACA,KAAAjB,YAAAL,GACA,MACA,UAAAc,OAAAC,aAAAQ,UACA,KAAAf,WAAAR,GACA,MACA,UAAAc,OAAAC,aAAAS,WACA,KAAAd,YAAAV,GACA,MACA,UAAAc,OAAAC,aAAAU,cACA,KAAAd,eAAAX,GACA,QAGAP,UACA,YAAAtB,SAGA,KAAAA,QAAA,EAEA1F,WAAA,KACA,KAAA0F,QAAA,GACA,MACA,IAEAsC,gBACA,KAAAjC,WAAAkD,cAAA,KAAAlD,WACA,KAAAA,UAAAmD,YAAA,KACA,KAAAtD,YACA,MAEAuD,iBAEA,KAAAnD,gBAAAiD,cAAA,KAAAjD,gBACA,KAAAA,eAAAkD,YAAA,KACA,KAAA3D,IAAA7B,UAAA,KAAAzL,OAAAuM,KACA,OAEAf,gBACA,KAAAkC,WAAAvI,QAAAgB,IACA,KAAAmH,IAAA9B,cAAA,KAAAxL,OAAAuM,GAAApG,KAEA,KAAAuH,WAAA,IAEAyD,YACA,KAAA9D,SAAA1B,OAAA,CAAAhN,EAAAnD,KACA6H,QAAAC,IAAA,YAAA3E,EAAA,IAAA2M,KAAAC,UAAA/P,IACA,eAAAmD,EAEA,KAAAuR,aAAA1U,GACA,YAAAmD,GACA,KAAAyQ,eAIAgC,UAAA7D,GACAlK,QAAAC,IAAA,cACA,KAAAiK,cAEA,KAAAc,MAAAC,UAAAC,UAAAhB,EACAkB,SAAAC,eAAA,aAAAE,OAAA,EACA,KAAAP,MAAAC,UAAAE,OAAA9K,MAAA,KACAL,QAAAC,IAAA,aAGA,KAAA8J,OAAAzI,oBAAA4I,EAAAC,IAEAnK,QAAAC,IAAA,UACA,KAAAkK,eACA,KAAAa,MAAAgD,YAAA9C,UAAAf,EACA,KAAAa,MAAAgD,YAAA7C,OAAA9K,MAAA,KACAL,QAAAC,IAAA,eAIA,KAAA8J,OAAApH,eAAAG,IACA,gBAAAG,MAEA,KAAAgH,IAAA9B,cAAA,KAAAxL,OAAAuM,GAAApG,GAGA,KAAAuH,WAAApR,KAAA6J,KAIA,KAAAiH,OAAAhH,cAAAE,IACA,aAAAA,EACA,KAAA4I,cACA,gBAAA5I,GACA,KAAA6I,oBAIAmC,eACA,IAAAC,EAAA,cAAAhT,KAAA,cACA,KAAAyB,OAAAS,eACA8Q,GAAA,SAAAvR,OAAAS,cAEAgO,SAAAlG,MAAAgJ,GAEAvC,iBAEA,YAAApF,OAAA/F,aAOA,KAAAuJ,OAAAvJ,aACAR,QAAAC,IAAA,eACA,KAAAgD,MAAA,QACA,KAAAtF,IAAA,gDACA,IAVAqC,QAAAC,IAAA,cACA,KAAAgD,MAAA,QACA,KAAAtF,IAAA,WACA,IAWAiO,QAAApK,GACAxB,QAAAC,IAAA,gBAEA,MAAAkB,EAAA,CACAgN,WAAA,KAAA5O,OAAA4O,YAEA,KAAApE,OAAA7I,KAAAC,GACA,cAAAjG,KAEA,KAAAqL,OAAAM,UAAA,KAAApJ,UAAA8F,KAAA2G,IACAlK,QAAAC,IAAA,SACA,KAAA8N,UAAA7D,GACA1I,MACAnB,MAAAsB,IACA3B,QAAAC,IAAA,SAAA0B,EAAAkD,SACA,KAAAlH,IAAAgE,EAAAkD,QAEA,KAAAkJ,YACAvM,MAIA,KAAA+E,OAAAiB,YAAAjE,KAAA2G,IACAlK,QAAAC,IAAA,SACA,KAAA8N,UAAA7D,GACA1I,MACAnB,MAAAsB,IACA3B,QAAAC,IAAA,SAAA0B,EAAAkD,SACA,KAAAlH,IAAAgE,EAAAkD,QAEA,KAAAkJ,YACAvM,OAIAsC,MAAAnG,GACA,KAAAA,MAEA,KAAAqN,MAAAS,UAAAH,QAEA,KAAAN,MAAAoD,YAAAjD,OAAA9K,MAAAsB,IACA3B,QAAAC,IAAA,cAGA,KAAAwK,WAAAkD,cAAA,KAAAlD,WACA,KAAAC,gBAAAiD,cAAA,KAAAjD,gBAEAhG,WAAA,KAEA,KAAAzB,MAAA,QAEA,KAAAsD,OAAAzC,QACA,KAAAiG,OAAAjG,QACA,KAAAkH,MAAAC,UAAAC,UAAA,KACA,KAAAF,MAAAgD,YAAA9C,UAAA,KAEArP,OAAAwS,IAAAC,YAAA,CACAnW,KAAA,CACAmD,IAAA,eAGA,OAEAiT,UAAAC,GACA,OAAAvG,KAAAO,MAAAC,mBAAA+F,MAGA/E,SAAA,CACAxM,cACA,sBAAAgG,OAEA1F,cACA,oBAAArC,MAEAgC,cACA,oBAAAhC,MAEA0C,iBACA,IAAA6Q,EAAAC,KAAAC,MAAA,KAAArE,SAAA,IACAsE,EAAA,KAAAtE,SAAA,GACAuE,EAAAJ,EAAA,UAKA,OAJAI,GAAAJ,EACAI,GAAA,IACAA,GAAAD,EAAA,UACAC,GAAAD,EACAC,IAGAC,UAEA,MAAA1O,EAAA,IAAA2O,IAAAlT,OAAAmT,SAAAC,MACA,KAAA/T,KAAAkF,EAAA8O,aAAAtU,IAAA,QACA,KAAAqD,OAAAgK,KAAAO,MAAApI,EAAA8O,aAAAtU,IAAA,WACA,KAAA8D,QAAA0B,EAAA8O,aAAAtU,IAAA,WACA,KAAA+P,OAAAvK,EAAA8O,aAAAtU,IAAA,UACA,KAAA+D,UAAA,KAAA4P,UAAAnO,EAAA8O,aAAAtU,IAAA,cACA,KAAA+B,OAAA,KAAA4R,UAAAnO,EAAA8O,aAAAtU,IAAA,WACA,KAAA2E,OAAA,KAAAgP,UAAAnO,EAAA8O,aAAAtU,IAAA,WAEA,KAAAqT,eAEA,KAAAhE,IAAA,IAAAvC,EAAA,KAAAhJ,QAAA,KAAAC,WAEA,KAAAmP,YAEA,KAAAD,iBAEAzC,SAAA1C,iBAAA,2BACA7M,OAAAwS,IAAAc,OAAArF,IACA9J,QAAAC,IAAA,QAAAgI,KAAAC,UAAA4B,IACA,KAAAA,QAGAjO,OAAAwS,IAAAC,YAAA,CACAnW,KAAA,CACAmD,IAAA,cAIA,KAAAkB,iBAAA,EAEA,KAAAyB,QAAA,KAAA0N,kBACA,KAAAC,QAAA,KAEA,KAAA1B,aACA,KAAAsB,eCpkBkV,ICQ9U,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCVA,GACfjR,KAAA,MACAqQ,WAAA,CACAwE,cCZ6T,ICQzT,G,UAAY,eACd,EACApT,EACAM,GACA,EACA,KACA,KACA,OAIa,I,QClBf,MAAM0Q,EAAe,CACpBE,gBAAiB,IACjBD,gBAAiB,IACjBE,WAAY,IACZC,WAAY,IACZC,WAAY,IACZE,WAAY,IACZD,WAAY,IACZE,UAAW,IACXC,WAAY,IACZC,cAAe,K,UCJhB2B,OAAIxW,UAAUkU,OAASuC,EACvBD,OAAI9P,OAAOgQ,eAAgB,EAE3B,IAAIF,OAAI,CACPrT,OAAQwT,GAAKA,EAAEC,KACbC,OAAO,S,oCCZV,W,uBCAAxV,EAAOD,QAAU,IAA0B,6B,mECA3CC,EAAOD,QAAU,IAA0B,2B,kCCA3C", "file": "js/app.c95dc742.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ChatVideo.vue?vue&type=style&index=0&id=4e2bfdb2&prod&lang=scss&scoped=true\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[_c('chat-video')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"chat-video\"},[(!_vm.isJSBridgeReady)?_c('div',{staticClass:\"mask\"}):_c('div',{staticClass:\"mask\",style:({ backgroundImage: 'url(' + _vm.friend.headImage + ')' })}),_c('audio',{ref:\"callAudio\",attrs:{\"loop\":\"true\"}},[_c('source',{attrs:{\"src\":require(\"@/assets/audio/call.wav\")}})]),_c('audio',{ref:\"handupAudio\",attrs:{\"x5-playsinline\":\"\",\"playsinline\":\"\",\"webkit-playsinline\":\"\"}},[_c('source',{attrs:{\"src\":require(\"@/assets/audio/handup.wav\")}})]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.isConnected||_vm.isVoiceMode),expression:\"!isConnected||isVoiceMode\"}],staticClass:\"friend-avatar\"},[_c('head-image',{attrs:{\"isReady\":_vm.isJSBridgeReady,\"url\":_vm.friend.headImage,\"name\":_vm.friend.showNickName,\"size\":200}},[_c('div',{staticClass:\"friend-name\"},[_vm._v(_vm._s(_vm.friend.showNickName))])])],1),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isConnected&&_vm.isVideoMode),expression:\"isConnected&&isVideoMode\"}],staticClass:\"video-box\"},[_c('div',{staticClass:\"video-friend\"},[_c('video',{ref:\"friendVideo\",staticClass:\"video-friend-video\",attrs:{\"id\":\"friendVideo\",\"x5-video-player-fullscreen\":\"true\",\"x5-playsinline\":\"\",\"playsinline\":\"\",\"webkit-playsinline\":\"\"}})]),_c('div',{staticClass:\"video-mine\"},[_c('video',{ref:\"mineVideo\",staticClass:\"video-mine-video\",class:_vm.isFacing?'reverse':'',attrs:{\"id\":\"mineVideo\",\"muted\":\"\",\"x5-video-player-fullscreen\":\"true\",\"x5-playsinline\":\"\",\"playsinline\":\"\",\"webkit-playsinline\":\"\"},domProps:{\"muted\":true}})])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.tip),expression:\"tip\"}],staticClass:\"tip\"},[_c('span',{staticClass:\"tip-text\"},[_vm._v(_vm._s(_vm.tip))])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isConnected),expression:\"isConnected\"}],staticClass:\"chat-time\"},[_c('span',{staticClass:\"chat-time-text\"},[_vm._v(_vm._s(_vm.chatTimeString))])]),_c('div',{staticClass:\"control-bar\"},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isConnected && _vm.isMicroPhone),expression:\"isConnected && isMicroPhone\"}],staticClass:\"icon iconfont icon-microphone-on icon-tool\",on:{\"click\":function($event){return _vm.onSwitchMicroPhone()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isConnected && !_vm.isMicroPhone),expression:\"isConnected && !isMicroPhone\"}],staticClass:\"icon iconfont icon-microphone-off icon-tool\",on:{\"click\":function($event){return _vm.onSwitchMicroPhone()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.isHost && !_vm.isConnected),expression:\"!isHost && !isConnected\"}],staticClass:\"icon iconfont icon-phone-reject icon-rtc red\",on:{\"click\":function($event){return _vm.onReject()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.isHost && !_vm.isConnected),expression:\"!isHost && !isConnected\"}],staticClass:\"icon iconfont icon-phone-accept icon-rtc\",on:{\"click\":function($event){return _vm.onAccept()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isHost && !_vm.isConnected),expression:\"isHost && !isConnected\"}],staticClass:\"icon iconfont icon-phone-reject icon-rtc red\",on:{\"click\":function($event){return _vm.onCancel()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isConnected),expression:\"isConnected\"}],staticClass:\"icon iconfont icon-phone-reject icon-150 icon-rtc red\",on:{\"click\":function($event){return _vm.onHandup()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isConnected && _vm.isVideoMode && _vm.isFacing),expression:\"isConnected && isVideoMode && isFacing\"}],staticClass:\"icon iconfont icon-camera-front icon-tool\",on:{\"click\":function($event){return _vm.onSwitchCamera()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isConnected && _vm.isVideoMode && !_vm.isFacing),expression:\"isConnected && isVideoMode && !isFacing\"}],staticClass:\"icon iconfont icon-camera-back icon-tool\",on:{\"click\":function($event){return _vm.onSwitchCamera()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isConnected && _vm.isVoiceMode && _vm.isSpeaker),expression:\"isConnected && isVoiceMode && isSpeaker\"}],staticClass:\"icon iconfont icon-speaker-on icon-tool\",on:{\"click\":function($event){return _vm.onSwitchSpeaker()}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isConnected && _vm.isVoiceMode && !_vm.isSpeaker),expression:\"isConnected && isVoiceMode && !isSpeaker\"}],staticClass:\"icon iconfont icon-speaker-off icon-tool\",on:{\"click\":function($event){return _vm.onSwitchSpeaker()}}})])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import axios from 'axios'\r\n\r\nlet createHttpRequest = function(baseUrl, loginInfo){\r\n\tif (window.plus) {\r\n\t\t// IOS系统必须使用plus.net.XMLHttpRequest，否则请求会报错\r\n\t\twindow.XMLHttpRequest = window.plus.net.XMLHttpRequest;\r\n\t}\r\n\r\n\tconst http = axios.create({\r\n\t\tbaseURL: baseUrl,\r\n\t\ttimeout: 1000 * 30,\r\n\t\theaders: {\r\n\t\t\t'Content-Type': 'application/json; charset=utf-8'\r\n\t\t}\r\n\t})\r\n\r\n\t/**\r\n\t * 请求拦截\r\n\t */\r\n\thttp.interceptors.request.use(config => {\r\n\t\tlet accessToken = loginInfo.accessToken;\r\n\t\tif (accessToken) {\r\n\t\t\tconfig.headers.accessToken = encodeURIComponent(accessToken);\r\n\t\t}\r\n\t\treturn config\r\n\t}, error => {\r\n\t\treturn Promise.reject(error)\r\n\t})\r\n\r\n\t/**\r\n\t * 响应拦截\r\n\t */\r\n\thttp.interceptors.response.use(async response => {\r\n\t\tif (response.data.code == 200) {\r\n\t\t\treturn response.data.data;\r\n\t\t} else if (response.data.code == 401) {\r\n\t\t\tconsole.log(\"token失效，尝试重新获取\")\r\n\t\t\tlet refreshToken = loginInfo.refreshToken;\r\n\t\t\t// 发送请求, 进行刷新token操作, 获取新的token\r\n\t\t\tloginInfo = await http({\r\n\t\t\t\tmethod: 'put',\r\n\t\t\t\turl: '/refreshToken',\r\n\t\t\t\theaders: {\r\n\t\t\t\t\trefreshToken: refreshToken\r\n\t\t\t\t}\r\n\t\t\t}).catch(() => {\r\n\t\t\t\tconsole.log(\"服务器请求异常\")\r\n\t\t\t})\r\n\t\t\t// 这里需要把headers清掉，否则请求时会报错，原因暂不详...\r\n\t\t\tif (typeof response.config.data != 'object') {\r\n\t\t\t\tresponse.config.headers = undefined;\r\n\t\t\t}\r\n\t\t\t// 重新发送刚才的请求\r\n\t\t\treturn http(response.config)\r\n\t\t} else {\r\n\t\t\treturn Promise.reject(response.data)\r\n\t\t}\r\n\t}, error => {\r\n\t\tconsole.log('服务器出了点小差，请稍后再试')\r\n\t\treturn Promise.reject(error)\r\n\t})\r\n\treturn http;\r\n}\r\n\r\n\r\nexport default createHttpRequest", "import http from \"@/common/request\"\r\n\r\nclass ImWebRtc {}\r\n\r\nImWebRtc.prototype.isEnable = function() {\r\n\twindow.RTCPeerConnection = window.RTCPeerConnection || window.webkitRTCPeerConnection || window\r\n\t\t.mozRTCPeerConnection;\r\n\twindow.RTCSessionDescription = window.RTCSessionDescription || window.webkitRTCSessionDescription || window\r\n\t\t.mozRTCSessionDescription;\r\n\twindow.RTCIceCandidate = window.RTCIceCandidate || window.webkitRTCIceCandidate || window\r\n\t\t.mozRTCIceCandidate;\r\n\treturn !!window.RTCPeerConnection;\r\n}\r\n\r\nImWebRtc.prototype.init = function(configuration) {\r\n\tthis.configuration = configuration;\r\n\t// 安卓11的webview有bug,需要提前发起一次连接\r\n\t// 参考博客： https://blog.csdn.net/logocool/article/details/136069364\r\n\tif(this.isAndroid11()){\r\n\t\tthis.fixAndroid()\r\n\t}\r\n}\r\n\r\nImWebRtc.prototype.setupPeerConnection = function(stream, callback) {\r\n\tthis.peerConnection = new RTCPeerConnection(this.configuration);\r\n\tthis.peerConnection.ontrack = (e) => {\r\n\t\t// 对方的视频流\r\n\t\tcallback(e.streams[0]);\r\n\t};\r\n\r\n\tif (stream) {\r\n\t\t// 把本地流添加进去\r\n\t\tstream.getTracks().forEach((track) => {\r\n\t\t\tthis.peerConnection.addTrack(track, stream);\r\n\t\t});\r\n\t}\r\n}\r\n\r\nImWebRtc.prototype.switchStream = function(stream) {\r\n\tlet senders = this.peerConnection.getSenders();\r\n\tlet videoTrack = stream.getVideoTracks()[0];\r\n\tlet audioTrack = stream.getAudioTracks()[0];\r\n\tsenders.forEach((sender) => {\r\n\t\tif (sender.track && sender.track.kind == 'video') {\r\n\t\t\tsender.replaceTrack(videoTrack);\r\n\t\t}\r\n\t\tif (sender.track && sender.track.kind == 'audio') {\r\n\t\t\tsender.replaceTrack(audioTrack);\r\n\t\t}\r\n\t});\r\n}\r\n\r\nImWebRtc.prototype.onIcecandidate = function(callback) {\r\n\tthis.peerConnection.onicecandidate = (event) => {\r\n\t\t// 追踪到候选信息\r\n\t\tif (event.candidate) {\r\n\t\t\tcallback(event.candidate)\r\n\t\t}\r\n\t}\r\n}\r\n\r\nImWebRtc.prototype.onStateChange = function(callback) {\r\n\t// 监听连接状态\r\n\tthis.peerConnection.oniceconnectionstatechange = (event) => {\r\n\t\tlet state = event.target.iceConnectionState;\r\n\t\tconsole.log(\"ICE连接状态变化: : \" + state)\r\n\t\tcallback(state)\r\n\t};\r\n}\r\n\r\nImWebRtc.prototype.createOffer = function() {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tconst offerParam = {};\r\n\t\tofferParam.offerToRecieveAudio = 1;\r\n\t\tofferParam.offerToRecieveVideo = 1;\r\n\t\t// 创建本地sdp信息\r\n\t\tthis.peerConnection.createOffer(offerParam).then((offer) => {\r\n\t\t\t// 设置本地sdp信息\r\n\t\t\tthis.peerConnection.setLocalDescription(offer);\r\n\t\t\t// 发起呼叫请求\r\n\t\t\tresolve(offer)\r\n\t\t}).catch((e) => {\r\n\t\t\treject(e)\r\n\t\t})\r\n\t});\r\n}\r\n\r\n\r\nImWebRtc.prototype.createAnswer = function(offer) {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\t// 设置远端的sdp\r\n\t\tthis.setRemoteDescription(offer);\r\n\t\t// 创建本地dsp\r\n\t\tconst offerParam = {};\r\n\t\tofferParam.offerToRecieveAudio = 1;\r\n\t\tofferParam.offerToRecieveVideo = 1;\r\n\t\tthis.peerConnection.createAnswer(offerParam).then((answer) => {\r\n\t\t\t// 设置本地sdp信息\r\n\t\t\tthis.peerConnection.setLocalDescription(answer);\r\n\t\t\t// 接受呼叫请求\r\n\t\t\tresolve(answer)\r\n\t\t}).catch((e) => {\r\n\t\t\treject(e)\r\n\t\t})\r\n\t});\r\n}\r\n\r\n\r\nImWebRtc.prototype.setRemoteDescription = function(offer) {\r\n\t// 设置对方的sdp信息\r\n\tthis.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));\r\n}\r\n\r\nImWebRtc.prototype.addIceCandidate = function(candidate) {\r\n\t// 添加对方的候选人信息\r\n\tthis.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));\r\n}\r\n\r\nImWebRtc.prototype.close = function(uid) {\r\n\t// 关闭RTC连接\r\n\tif (this.peerConnection) {\r\n\t\tthis.peerConnection.close();\r\n\t\tthis.peerConnection.onicecandidate = null;\r\n\t\tthis.peerConnection.onaddstream = null;\r\n\t}\r\n}\r\n\r\n\r\nImWebRtc.prototype.isAndroid11 = function() {\r\n\tif (window.plus) {\r\n\t\tconst deviceInfo = navigator.userAgent;\r\n\t\tconst androidVersion = deviceInfo.match(/Android ([\\d.]+)/);\r\n\t\tif (androidVersion && androidVersion.length === 2) {\r\n\t\t\tconsole.log(\"androidVersion:\",androidVersion)\r\n\t\t\treturn androidVersion[1]=='11'\r\n\t\t}\r\n\t}\r\n\treturn false;\r\n}\r\n\r\nImWebRtc.prototype.fixAndroid = function() {\r\n\tconsole.log(\"fixAndroid close\")\r\n\tthis.configuration.iceCandidatePoolSize = 1;\r\n\tlet peer = new RTCPeerConnection(this.configuration);\r\n\tpeer.createOffer({\r\n\t\tofferToReceiveAudio: true,\r\n\t\tofferToReceiveVideo: true\r\n\t}).then((offer) => {\r\n\t\tpeer.setLocalDescription(offer);\r\n\t\tsetTimeout(() => {\r\n\t\t\tpeer.close()\r\n\t\t\tconsole.log(\"fixAndroid close\")\r\n\t\t}, 1000)\r\n\t})\t\r\n}\r\n\r\n\r\nexport default ImWebRtc;", "const requestPermissions = (scope, message) => {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\twindow.plus.android.requestPermissions(\r\n\t\t\t[\"android.permission.\" + scope],\r\n\t\t\tfunction(resultObj) {\r\n\t\t\t\tconsole.log(resultObj, \"resultObj\");\r\n\t\t\t\tresolve(resultObj);\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tfunction(error) {\r\n\t\t\t\treject()\r\n\t\t\t}\r\n\t\t);\r\n\t});\r\n}\r\n\r\n\r\n// 跳转权限设置\r\nlet topMessageView;\r\nconst createTopMessage = (title, message) => {\r\n\ttopMessageView = new window.plus.nativeObj.View(\"topMessageView\", {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tbackgroundColor: \"rgba(0,0,0,0.85)\",\r\n\t});\r\n\ttopMessageView.drawText(\r\n\t\ttitle, {\r\n\t\t\ttop: \"-50px\",\r\n\t\t\tleft: \"10%\",\r\n\t\t\twidth: \"80%\",\r\n\t\t\theight: \"80%\",\r\n\t\t}, {\r\n\t\t\tcolor: \"#ffffff\",\r\n\t\t\tsize: \"22px\",\r\n\t\t}\r\n\t);\r\n\ttopMessageView.drawText(\r\n\t\tmessage, {\r\n\t\t\ttop: \"0px\",\r\n\t\t\tleft: \"10%\",\r\n\t\t\twidth: \"80%\",\r\n\t\t\theight: \"80%\",\r\n\t\t}, {\r\n\t\t\tcolor: \"#ffffff\",\r\n\t\t\twhiteSpace: \"normal\",\r\n\t\t}\r\n\t);\r\n\t// 显示消息提示框\r\n\ttopMessageView.show();\r\n}\r\n\r\nconst checkAndRequest = async (scope, title, messageTip, settingTip) => {\r\n\tif (window.plus && window.plus.os.name !== \"iOS\") {\r\n\t\tlet t =setTimeout(() => createTopMessage(title, messageTip), 300);\r\n\t\tlet res = await requestPermissions(scope, settingTip);\r\n\t\tclearTimeout(t);\r\n\t\ttopMessageView && topMessageView.close();\r\n\t\tconsole.log(\"res:\", res);\r\n\t\tif (!res.granted[0]) {\r\n\t\t\t// 无权限\r\n\t\t\treturn false;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nconst storage = async() => {\r\n\tconst scope = \"WRITE_EXTERNAL_STORAGE\"\r\n\tconst title = \"访问媒体和文件权限说明\";\r\n\tconst messageTip = \"用于用户发送图片、视频、文件或上传头像等场景\";\r\n\tconst settingTip = \"访问媒体和文件权限未获得,此权限用于用户发送图片、视频、文件或上传头像等场景,请前往设置中打开\";\r\n\treturn checkAndRequest(scope, title, messageTip, settingTip);\r\n}\r\n\r\nconst camera = async () => {\r\n\tconst scope = \"CAMERA\"\r\n\tconst title = \"相机使用权限说明\";\r\n\tconst messageTip = \"用于拍照、录像、视频通话等场景\";\r\n\tconst settingTip = \"相机使用权限未获得,此权限用于拍照、录像、视频通话等场景,请前往设置中打开\";\r\n\treturn checkAndRequest(scope, title, messageTip, settingTip);\r\n}\r\n\r\n\r\nconst micro = async() => {\r\n\tconst scope = \"RECORD_AUDIO\"\r\n\tconst title = \"麦克风使用权限说明\";\r\n\tconst messageTip = \"用于拍摄、录制语音消息、视频或语音通话等场景\";\r\n\tconst settingTip = \"麦克风使用权限未获得,此权限用于用于拍摄、录制语音消息、视频或语音通话等场景,请前往设置中打开\";\r\n\treturn checkAndRequest(scope, title, messageTip, settingTip);\r\n}\r\n\r\nexport default {\r\n\tstorage,\r\n\tcamera,\r\n\tmicro\r\n}", "import permission from \"./permission\"\r\n\r\nclass ImCamera {\r\n\tconstructor() {\r\n\t\tthis.stream = null;\r\n\t}\r\n}\r\n\r\nImCamera.prototype.isEnable = function() {\r\n\treturn !!navigator && !!navigator.mediaDevices && !!navigator.mediaDevices.getUserMedia;\r\n}\r\n\r\nImCamera.prototype.openVideo = async function(isFacing) {\r\n\tif (this.stream) {\r\n\t\tthis.close();\r\n\t}\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tsetTimeout(async () => {\r\n\t\t\tif (!await permission.camera()) {\r\n\t\t\t\treturn reject({\r\n\t\t\t\t\tmessage: \"未能获取摄像头访问权限\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif (!await permission.micro()) {\r\n\t\t\t\treturn reject({\r\n\t\t\t\t\tmessage: \"未能获取麦克风权限\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tlet ratio = window.devicePixelRatio || 1;\t\r\n\t\t\tlet facingMode = isFacing ? \"user\" : \"environment\";\r\n\t\t\tlet constraints = {\r\n\t\t\t\tvideo: {\r\n\t\t\t\t\t// 对于webrtc,屏幕长宽是反过来的\r\n\t\t\t\t\twidth: window.screen.height * ratio,\r\n\t\t\t\t\theight: window.screen.width * ratio,\r\n\t\t\t\t\tfacingMode: facingMode\r\n\t\t\t\t},\r\n\t\t\t\taudio: {\r\n\t\t\t\t\techoCancellation: true, //音频开启回音消除\r\n\t\t\t\t\tnoiseSuppression: true // 开启降噪\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tnavigator.mediaDevices.getUserMedia(constraints).then((stream) => {\r\n\t\t\t\tconsole.log(\"摄像头打开\")\r\n\t\t\t\tthis.stream = stream;\r\n\t\t\t\tresolve(stream);\r\n\t\t\t}).catch((e) => {\r\n\t\t\t\tconsole.log(\"摄像头未能正常打开\", e)\r\n\t\t\t\treject({\r\n\t\t\t\t\tcode: 0,\r\n\t\t\t\t\tmessage: \"摄像头未能正常打开\"\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t})\r\n\t})\r\n}\r\n\r\nImCamera.prototype.openAudio = function() {\r\n\tif (this.stream) {\r\n\t\tthis.close();\r\n\t}\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tsetTimeout(async () => {\r\n\t\t\tif (!await permission.micro()) {\r\n\t\t\t\treturn reject({\r\n\t\t\t\t\tcode: 0,\r\n\t\t\t\t\tmessage: \"未能获取麦克风权限\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tlet constraints = {\r\n\t\t\t\tvideo: false,\r\n\t\t\t\taudio: {\r\n\t\t\t\t\techoCancellation: true, //音频开启回音消除\r\n\t\t\t\t\tnoiseSuppression: true // 开启降噪\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tnavigator.mediaDevices.getUserMedia(constraints).then((stream) => {\r\n\t\t\t\tthis.stream = stream;\r\n\t\t\t\tresolve(stream);\r\n\t\t\t}).catch(() => {\r\n\t\t\t\tconsole.log(\"麦克风未能正常打开\")\r\n\t\t\t\treject({\r\n\t\t\t\t\tcode: 0,\r\n\t\t\t\t\tmessage: \"麦克风未能正常打开\"\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t})\r\n\t})\r\n}\r\n\r\nImCamera.prototype.close = function() {\r\n\t// 停止流\r\n\tif (this.stream) {\r\n\t\tthis.stream.getTracks().forEach((track) => {\r\n\t\t\ttrack.stop();\r\n\t\t});\r\n\t\tthis.stream = null;\r\n\t}\r\n}\r\n\r\nexport default ImCamera;", "import createHttpRequest from './request'\r\n\r\nclass ImApi {\r\n\tconstructor(baseUrl, loginInfo) {\r\n\t\tthis.http = createHttpRequest(baseUrl, loginInfo);\r\n\t}\r\n}\r\n\r\n\r\nImApi.prototype.setup = function(uid, mode) {\r\n\treturn this.http({\r\n\t\turl: `/webrtc/private/setup?uid=${uid}&mode=${mode}`,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\nImApi.prototype.accept = function(uid) {\r\n\treturn this.http({\r\n\t\turl: `/webrtc/private/accept?uid=${uid}`,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\n\r\nImApi.prototype.handup = function(uid) {\r\n\treturn this.http({\r\n\t\turl: `/webrtc/private/handup?uid=${uid}`,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\nImApi.prototype.cancel = function(uid) {\r\n\treturn this.http({\r\n\t\turl: `/webrtc/private/cancel?uid=${uid}`,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\nImApi.prototype.reject = function(uid) {\r\n\treturn this.http({\r\n\t\turl: `/webrtc/private/reject?uid=${uid}`,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\nImApi.prototype.failed = function(uid, reason) {\r\n\treturn this.http({\r\n\t\turl: `/webrtc/private/failed?uid=${uid}&reason=${reason}`,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\nImApi.prototype.offer = function(uid, offer) {\r\n\treturn this.http({\r\n\t\turl: `/webrtc/private/offer?uid=${uid}`,\r\n\t\tmethod: 'post',\r\n\t\tdata: JSON.stringify(offer)\r\n\t});\r\n}\r\n\r\nImApi.prototype.answer = function(uid, answer) {\r\n\treturn this.http({\r\n\t\turl: `/webrtc/private/answer?uid=${uid}`,\r\n\t\tmethod: 'post',\r\n\t\tdata: JSON.stringify(answer)\r\n\t});\r\n}\r\n\r\nImApi.prototype.sendCandidate = function(uid, candidate) {\r\n\treturn this.http({\r\n\t\turl: `/webrtc/private/candidate?uid=${uid}`,\r\n\t\tmethod: 'post',\r\n\t\tdata: JSON.stringify(candidate)\r\n\t});\r\n}\r\n\r\nImApi.prototype.heartbeat = function(uid) {\r\n\treturn this.http({\r\n\t\turl: `/webrtc/private/heartbeat?uid=${uid}`,\r\n\t\tmethod: 'post'\r\n\t})\r\n}\r\n\r\nexport default ImApi;", "\r\n/**\r\n * 接收来自uniapp的消息\r\n */\r\nclass UniEvent{}\r\n\r\nUniEvent.prototype.listen = function(callback){\r\n\t// APP\r\n\twindow.onEvent = (e)=>{\r\n\t\tlet event = JSON.parse(decodeURIComponent(e));\r\n\t\tcallback(event.key,event.data)\r\n\t}\r\n\t// H5\r\n\twindow.addEventListener('message', function (e) {\r\n\t\tconst event = e.data;\r\n\t\tcallback(event.key,event.data)\r\n\t},false)\r\n\t\r\n}\r\n\r\nexport default UniEvent;", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"head-image\"},[(_vm.isReady&&_vm.url)?_c('img',{staticClass:\"avatar-image\",attrs:{\"src\":_vm.url}}):_c('div',{staticClass:\"avatar-text\",style:(_vm.avatarTextStyle)},[_vm._v(\" \"+_vm._s(_vm.name.substring(0,1).toUpperCase())+\" \")]),_vm._t(\"default\")],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<div class=\"head-image\">\r\n\t\t<img class=\"avatar-image\" v-if=\"isReady&&url\" :src=\"url\" />\r\n\t\t<div class=\"avatar-text\" v-else :style=\"avatarTextStyle\">\r\n\t\t\t{{name.substring(0,1).toUpperCase()}}\r\n\t\t</div>\r\n\t\t<slot></slot>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tname: \"headImage\",\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcolors: [\"#7dd24b\", \"#c7515a\", \"#db68ef\", \"#15d29b\", \"#85029b\",\r\n\t\t\t\t\"#c9b455\", \"#fb2609\", \"#bda818\", \"#af0831\", \"#326eb6\"\r\n\t\t\t]\r\n\t\t}\r\n\t},\r\n\tprops: {\r\n\t\tid: {\r\n\t\t\ttype: Number\r\n\t\t},\r\n\t\tsize: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 50\r\n\t\t},\r\n\t\turl: {\r\n\t\t\ttype: String\r\n\t\t},\r\n\t\tname: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: \"?\"\r\n\t\t},\r\n\t\tisReady: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t}\r\n\t},\r\n\tmethods: {},\r\n\tcomputed: {\r\n\t\tavatarTextStyle() {\r\n\t\t\treturn `color:${this.textColor};`\r\n\t\t},\r\n\t\ttextColor() {\r\n\t\t\tlet hash = 0;\r\n\t\t\tfor (var i = 0; i < this.name.length; i++) {\r\n\t\t\t\thash += this.name.charCodeAt(i);\r\n\t\t\t}\r\n\t\t\treturn this.colors[hash % this.colors.length];\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.head-image {\r\n\tposition: relative;\r\n\tcursor: pointer;\r\n\r\n\t.avatar-image {\r\n\t\tposition: relative;\r\n\t\twidth: 300px;\r\n\t\theight: 300px;\r\n\t\toverflow: hidden;\r\n\t\tborder-radius: 10%;\r\n\t}\r\n\r\n\t.avatar-text {\r\n\t\tbackground-color: #f2f2f2;\r\n\t\t/* 默认背景色 */\r\n\t\tborder-radius: 50%;\r\n\t\t/* 圆角效果 */\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder: 1px solid #ccc;\r\n\t\twidth: 250px;\r\n\t\theight: 250px;\r\n\t\tfont-size: 150px;\r\n\t}\r\n}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HeadImage.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HeadImage.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./HeadImage.vue?vue&type=template&id=4694a9a0&scoped=true\"\nimport script from \"./HeadImage.vue?vue&type=script&lang=js\"\nexport * from \"./HeadImage.vue?vue&type=script&lang=js\"\nimport style0 from \"./HeadImage.vue?vue&type=style&index=0&id=4694a9a0&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4694a9a0\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div class=\"chat-video\">\r\n\t\t<div v-if=\"!isJSBridgeReady\" class=\"mask\"></div>\r\n\t\t<div v-else class=\"mask\" :style=\"{ backgroundImage: 'url(' + friend.headImage + ')' }\"></div>\r\n\t\t<audio ref=\"callAudio\" loop=\"true\">\r\n\t\t\t<source src=\"@/assets/audio/call.wav\">\r\n\t\t</audio>\r\n\t\t<audio ref=\"handupAudio\" x5-playsinline playsinline webkit-playsinline>\r\n\t\t\t<source src=\"@/assets/audio/handup.wav\">\r\n\t\t</audio>\r\n\t\t<div class=\"friend-avatar\" v-show=\"!isConnected||isVoiceMode\">\r\n\t\t\t<head-image :isReady=\"isJSBridgeReady\" :url=\"friend.headImage\" :name=\"friend.showNickName\" :size=\"200\">\r\n\t\t\t\t<div class=\"friend-name\">{{friend.showNickName}}</div>\r\n\t\t\t</head-image>\r\n\t\t</div>\r\n\t\t<div class=\"video-box\" v-show=\"isConnected&&isVideoMode\">\r\n\t\t\t<div class=\"video-friend\">\r\n\t\t\t\t<video class=\"video-friend-video\" ref=\"friendVideo\" id=\"friendVideo\" x5-video-player-fullscreen=\"true\"\r\n\t\t\t\t\tx5-playsinline playsinline webkit-playsinline>\r\n\t\t\t\t</video>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"video-mine\">\r\n\t\t\t\t<video class=\"video-mine-video\" :class=\"isFacing?'reverse':''\" id=\"mineVideo\" ref=\"mineVideo\" muted\r\n\t\t\t\t\tx5-video-player-fullscreen=\"true\" x5-playsinline playsinline webkit-playsinline></video>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"tip\" v-show=\"tip\">\r\n\t\t\t<span class=\"tip-text\">{{tip}}</span>\r\n\t\t</div>\r\n\t\t<div v-show=\"isConnected\" class=\"chat-time\">\r\n\t\t\t<span class=\"chat-time-text\">{{chatTimeString}}</span>\r\n\t\t</div>\r\n\t\t<div class=\"control-bar\">\r\n\t\t\t<div v-show=\"isConnected && isMicroPhone\" class=\"icon iconfont icon-microphone-on icon-tool\"\r\n\t\t\t\t@click=\"onSwitchMicroPhone()\"></div>\r\n\t\t\t<div v-show=\"isConnected && !isMicroPhone\" class=\"icon iconfont icon-microphone-off icon-tool\"\r\n\t\t\t\t@click=\"onSwitchMicroPhone()\"></div>\r\n\t\t\t<div v-show=\"!isHost && !isConnected\" class=\"icon iconfont icon-phone-reject icon-rtc red\"\r\n\t\t\t\t@click=\"onReject()\"></div>\r\n\t\t\t<div v-show=\"!isHost && !isConnected\" class=\"icon iconfont icon-phone-accept icon-rtc\" @click=\"onAccept()\">\r\n\t\t\t</div>\r\n\t\t\t<div v-show=\"isHost && !isConnected\" class=\"icon iconfont icon-phone-reject icon-rtc red\"\r\n\t\t\t\t@click=\"onCancel()\">\r\n\t\t\t</div>\r\n\t\t\t<div v-show=\"isConnected\" class=\"icon iconfont icon-phone-reject icon-150 icon-rtc red\" @click=\"onHandup()\">\r\n\t\t\t</div>\r\n\t\t\t<div v-show=\"isConnected && isVideoMode && isFacing\" class=\"icon iconfont icon-camera-front icon-tool\"\r\n\t\t\t\t@click=\"onSwitchCamera()\"></div>\r\n\t\t\t<div v-show=\"isConnected && isVideoMode && !isFacing\" class=\"icon iconfont icon-camera-back icon-tool\"\r\n\t\t\t\t@click=\"onSwitchCamera()\"></div>\r\n\t\t\t<div v-show=\"isConnected && isVoiceMode && isSpeaker\" class=\"icon iconfont icon-speaker-on icon-tool\"\r\n\t\t\t\t@click=\"onSwitchSpeaker()\"></div>\r\n\t\t\t<div v-show=\"isConnected && isVoiceMode && !isSpeaker\" class=\"icon iconfont icon-speaker-off icon-tool\"\r\n\t\t\t\t@click=\"onSwitchSpeaker()\"></div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport ImWebRtc from '@/common/webrtc'\r\nimport ImCamera from '@/common/camera'\r\nimport ImApi from '@/common/api'\r\nimport UniEvent from '@/common/uniEvent'\r\nimport HeadImage from '@/components/HeadImage'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tenv: {},\r\n\t\t\tisJSBridgeReady: false,\r\n\t\t\tmode: \"video\", // video: 视频聊天,voice :语音聊天\r\n\t\t\tcamera: new ImCamera(), // 摄像头和麦克风\r\n\t\t\twebrtc: new ImWebRtc(), // webrtc相关\r\n\t\t\tuniEvent: new UniEvent(), // 接受来自uniapp的消息\r\n\t\t\tAPI: null, // API接口请求对象\r\n\t\t\tlocalStream: null, // 本地视频\r\n\t\t\tremoteStream: null, // 对方视频\r\n\t\t\tisHost: true, // 是否呼叫方\r\n\t\t\tisMicroPhone: true, // 是否开启麦克风\r\n\t\t\tisFacing: true, // 是否前置摄像头\r\n\t\t\tisSpeaker: true, // 是否开启扬声器\r\n\t\t\tisLock: false, // 防抖锁定标志，如果重复点击接听会报错\r\n\t\t\ttip: \"\", // 提示文字\r\n\t\t\tstate: \"INIT\", // INIT:初始状态  WAITING:等待呼叫或接听 CHATING:聊天中  CLOSE:关闭 ERROR:出现异常\r\n\t\t\tcandidates: [], // 候选信息\r\n\t\t\tchatTime: 0, // 聊天时长\r\n\t\t\taudioPlayer: null, // app的音频播放器\r\n\t\t\tdisconnectTimer: null, // 断开连接超时退出定时器\r\n\t\t\tchatTimer: null, // 聊天计时器\r\n\t\t\theartbeatTimer: null, // 心跳定时器\r\n\t\t\tbaseUrl: \"\", // 服务器基础接口路径\r\n\t\t\tloginInfo: {}, // token信息\r\n\t\t\tuserId: null, // 当前用户id\r\n\t\t\tfriend: {}, // 好友信息\r\n\t\t\tconfig: {} // 系统配置信息\r\n\t\t};\r\n\t},\r\n\tcomponents: {\r\n\t\tHeadImage\r\n\t},\r\n\tmethods: {\r\n\t\tonSwitchMicroPhone() {\r\n\t\t\tthis.isMicroPhone = !this.isMicroPhone;\r\n\t\t\tif (this.localStream) {\r\n\t\t\t\tthis.localStream.getTracks().forEach((track => {\r\n\t\t\t\t\tif (track.kind === 'audio') {\r\n\t\t\t\t\t\ttrack.enabled = this.isMicroPhone\r\n\t\t\t\t\t}\r\n\t\t\t\t}))\r\n\t\t\t}\r\n\t\t\tconsole.log(\"麦克风:\" + this.isMicroPhone)\r\n\t\t},\r\n\t\tonSwitchCamera() {\r\n\t\t\tthis.isFacing = !this.isFacing;\r\n\t\t\tthis.camera.openVideo(this.isFacing).then((localStream) => {\r\n\t\t\t\tthis.localStream = localStream;\r\n\t\t\t\t// 如果麦克风已经关闭，需要禁用声音频道\r\n\t\t\t\tif (!this.isMicroPhone) {\r\n\t\t\t\t\tthis.localStream.getTracks().forEach((track => {\r\n\t\t\t\t\t\tif (track.kind === 'audio') {\r\n\t\t\t\t\t\t\ttrack.enabled = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}))\r\n\t\t\t\t}\r\n\t\t\t\t// 切换传输的视频流\r\n\t\t\t\tthis.webrtc.switchStream(localStream);\r\n\t\t\t\tlet cameraName = this.isFacing ? \"前置\" : \"后置\"\r\n\t\t\t\t// 显示本地视频\r\n\t\t\t\tthis.$refs.mineVideo.srcObject = localStream;\r\n\t\t\t\tthis.$refs.mineVideo.play().catch(() => {\r\n\t\t\t\t\tconsole.log(\"播放本地视频失败\")\r\n\t\t\t\t});\r\n\t\t\t\tconsole.log(\"摄像头切换:\" + cameraName);\r\n\t\t\t}).catch((e) => {\r\n\t\t\t\tconsole.log(\"摄像头切换失败:\" + e.message)\r\n\t\t\t})\r\n\t\t},\r\n\t\tonSwitchSpeaker() {\r\n\t\t\tthis.isSpeaker = !this.isSpeaker;\r\n\t\t\tconst video = document.getElementById(\"friendVideo\");\r\n\t\t\tvideo.pause();\r\n\t\t\tvideo.muted = !this.isSpeaker;\r\n\t\t\tvideo.play();\r\n\t\t\t// if(window.plus){\r\n\t\t\t// \tif(!this.audioPlayer){\r\n\t\t\t// \t\tthis.audioPlayer = window.plus.audio.createPlayer();\r\n\t\t\t// \t}\r\n\t\t\t// \t// 扬声器\r\n\t\t\t// \tlet route=window.plus.audio.ROUTE_SPEAKER;\r\n\t\t\t// \tif(!this.isSpeaker){\r\n\t\t\t// \t\t// 听筒\r\n\t\t\t// \t\troute = window.plus.audio.ROUTE_EARPIECE\r\n\t\t\t// \t}\r\n\t\t\t// \tthis.audioPlayer.setRoute(route);\r\n\t\t\t// }\r\n\t\t\tconsole.log(\"扬声器切换:\" + this.isSpeaker);\r\n\t\t},\r\n\t\tonCall() {\r\n\t\t\t// 发起呼叫\r\n\t\t\tthis.API.setup(this.friend.id, this.mode).then(() => {\r\n\t\t\t\tthis.tip = \"等待对方接受邀请...\";\r\n\t\t\t\tthis.state = \"WAITING\";\r\n\t\t\t\tthis.$refs.callAudio.play().catch((e) => {\r\n\t\t\t\t\tconsole.log(\"播放呼叫语音失败\")\r\n\t\t\t\t});\r\n\t\t\t}).catch((e) => {\r\n\t\t\t\tthis.close(e.message);\r\n\t\t\t});\r\n\t\t},\r\n\t\tonAccept() {\r\n\t\t\t// 用锁实现防抖，如果用户手快多次点击接听会报错\r\n\t\t\tif (!this.tryLock()) {\r\n\t\t\t\tconsole.log(\"accept防抖触发\")\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t// 停止呼叫铃声\r\n\t\t\tthis.$refs.callAudio.pause();\r\n\t\t\t// 检查设备\r\n\t\t\tif (!this.checkDevEnable()) {\r\n\t\t\t\tthis.API.failed(this.friend.id, \"对方设备不支持通话\");\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t// 初始化webrtc\r\n\t\t\tthis.initRtc(() => {\r\n\t\t\t\t// 接受对方的邀请\r\n\t\t\t\tthis.API.accept(this.friend.id).catch((e) => {\r\n\t\t\t\t\tthis.tip = e.message;\r\n\t\t\t\t});\r\n\t\t\t})\r\n\t\t},\r\n\t\tonReject() {\r\n\t\t\tif (!this.tryLock()) {\r\n\t\t\t\tconsole.log(\"reject防抖触发\")\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t// 拒绝通话\r\n\t\t\tthis.API.reject(this.friend.id);\r\n\t\t\tthis.close(\"您已拒绝通话\");\r\n\t\t},\r\n\t\tonCancel() {\r\n\t\t\tif (!this.tryLock()) {\r\n\t\t\t\tconsole.log(\"cancel防抖触发\")\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t// 取消呼叫\r\n\t\t\tthis.API.cancel(this.friend.id);\r\n\t\t\tthis.close(\"您已取消呼叫\");\r\n\t\t},\r\n\t\tonHandup() {\r\n\t\t\t// 挂断\r\n\t\t\tthis.API.handup(this.friend.id);\r\n\t\t\tthis.close(\"您已挂断,通话结束\");\r\n\t\t},\r\n\t\tonConnected() {\r\n\t\t\tconsole.log(\"webrtc连接成功\")\r\n\t\t\tthis.tip = \"\";\r\n\t\t\tthis.disconnectTimer && clearTimeout(this.disconnectTimer)\r\n\t\t},\r\n\t\tonDisconnected() {\r\n\t\t\tconsole.log(\"webrtc网络断开\")\r\n\t\t\tthis.tip = \"当前通话质量不佳\";\r\n\t\t\t// 15s秒内没有重连回来，就中断视频\r\n\t\t\tthis.disconnectTimer = setTimeout(() => {\r\n\t\t\t\tthis.close(\"网络异常，通话结束\");\r\n\t\t\t}, 15000);\r\n\t\t},\r\n\t\tonNavBack() {\r\n\t\t\tconsole.log(\"强制退出\");\r\n\t\t\tif (this.isHost && this.state == \"WAITING\") {\r\n\t\t\t\t// 强制终止呼叫\r\n\t\t\t\tconsole.log(\"强制终止呼叫\");\r\n\t\t\t\tthis.API.cancel(this.friend.id);\r\n\t\t\t} else if (!this.isHost && this.state == \"WAITING\") {\r\n\t\t\t\t// 强制拒绝接听\r\n\t\t\t\tconsole.log(\"强制拒绝接听\");\r\n\t\t\t\tthis.API.reject(this.friend.id);\r\n\t\t\t} else if (this.state == \"CHATING\") {\r\n\t\t\t\t// 强制退出聊天\r\n\t\t\t\tthis.API.handup(this.friend.id);\r\n\t\t\t}\r\n\t\t},\r\n\t\tonRTCSetup(msg) {\r\n\t\t\t// 收到对方的呼叫\r\n\t\t\tthis.state = \"WAITING\";\r\n\t\t\tthis.tip = `邀请您${this.isVideoMode? \"视频\":\"语音\"}通话...`;\r\n\t\t\tthis.$refs.callAudio.play().catch((e) => {\r\n\t\t\t\tconsole.log(\"播放呼叫语音失败\")\r\n\t\t\t});\r\n\t\t},\r\n\t\tonRTCAccept(msg) {\r\n\t\t\t// 收到对方接受通话消息\r\n\t\t\tif (msg.selfSend) {\r\n\t\t\t\t// 我在其他终端接受了对方的通话\r\n\t\t\t\tthis.close(\"已在其他设备接听\");\r\n\t\t\t} else {\r\n\t\t\t\t// 对方接受了通话\r\n\t\t\t\tthis.webrtc.createOffer().then((offer) => {\r\n\t\t\t\t\tthis.$refs.callAudio.pause();\r\n\t\t\t\t\t// 推送offer给对方\r\n\t\t\t\t\tthis.API.offer(this.friend.id, offer);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tonRTCRejct(msg) {\r\n\t\t\tif (msg.selfSend) {\r\n\t\t\t\t// 我在其他终端拒绝了对方的通话\r\n\t\t\t\tthis.close(\"已在其他设备拒绝通话\");\r\n\t\t\t} else {\r\n\t\t\t\t// 对方拒绝了我的通话\r\n\t\t\t\tthis.close(\"对方拒绝了您的通话请求\");\r\n\t\t\t}\r\n\t\t},\r\n\t\tonRTCCancel(msg) {\r\n\t\t\tthis.close(\"对方取消了呼叫\");\r\n\t\t},\r\n\t\tonRTCFailed(msg) {\r\n\t\t\tif (msg.selfSend) {\r\n\t\t\t\tthis.close(\"您未接听\");\r\n\t\t\t} else {\r\n\t\t\t\tthis.close(msg.content);\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tonRTCHandup(msg) {\r\n\t\t\tthis.close(\"对方已挂断,通话结束\", \"对方已挂断\");\r\n\t\t},\r\n\t\tonRTCOffer(msg) {\r\n\t\t\tconst offer = JSON.parse(msg.content);\r\n\t\t\tthis.webrtc.createAnswer(offer).then((answer) => {\r\n\t\t\t\t// 推送answer给对方\r\n\t\t\t\tthis.API.answer(this.friend.id, answer);\r\n\t\t\t})\r\n\t\t\t// 状态为聊天中\r\n\t\t\tthis.state = 'CHATING';\r\n\t\t\t// 记录时长\r\n\t\t\tthis.startChatTime();\r\n\t\t},\r\n\t\tonRTCAnswer(msg) {\r\n\t\t\tconst answer = JSON.parse(msg.content);\r\n\t\t\tthis.webrtc.setRemoteDescription(answer);\r\n\t\t\t// 进入聊天状态\r\n\t\t\tthis.state = \"CHATING\";\r\n\t\t\t// 推送候选人信息\r\n\t\t\tthis.sendCandidate();\r\n\t\t\t// 开始计时\r\n\t\t\tthis.startChatTime();\r\n\t\t},\r\n\t\tonRTCCandidate(msg) {\r\n\t\t\tthis.webrtc.addIceCandidate(JSON.parse(msg.content));\r\n\t\t},\r\n\t\tonRTCMessage(msg) {\r\n\t\t\t// 防止被其他人消息干扰\r\n\t\t\tif (msg.sendId != this.friend.id &&\r\n\t\t\t\tmsg.sendId != this.userId &&\r\n\t\t\t\tmsg.type != this.$enums.MESSAGE_TYPE.RTC_SETUP_VIDEO &&\r\n\t\t\t\tmsg.type != this.$enums.MESSAGE_TYPE.RTC_SETUP_VOICE) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t// RTC信令处理\r\n\t\t\tswitch (msg.type) {\r\n\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_SETUP_VIDEO:\r\n\t\t\t\t\tthis.onRTCSetup(msg)\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_SETUP_VOICE:\r\n\t\t\t\t\tthis.onRTCSetup(msg)\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_ACCEPT:\r\n\t\t\t\t\tthis.onRTCAccept(msg)\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_REJECT:\r\n\t\t\t\t\tthis.onRTCRejct(msg)\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_CANCEL:\r\n\t\t\t\t\tthis.onRTCCancel(msg)\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_HANDUP:\r\n\t\t\t\t\tthis.onRTCHandup(msg)\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_FAILED:\r\n\t\t\t\t\tthis.onRTCFailed(msg)\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_OFFER:\r\n\t\t\t\t\tthis.onRTCOffer(msg)\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_ANSWER:\r\n\t\t\t\t\tthis.onRTCAnswer(msg)\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase this.$enums.MESSAGE_TYPE.RTC_CANDIDATE:\r\n\t\t\t\t\tthis.onRTCCandidate(msg)\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t},\r\n\t\ttryLock() {\r\n\t\t\tif (this.isLock) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tthis.isLock = true;\r\n\t\t\t// 2s后解锁\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.isLock = false;\r\n\t\t\t}, 2000)\r\n\t\t\treturn true;\r\n\t\t},\r\n\t\tstartChatTime() {\r\n\t\t\tthis.chatTimer && clearInterval(this.chatTimer);\r\n\t\t\tthis.chatTimer = setInterval(() => {\r\n\t\t\t\tthis.chatTime++;\r\n\t\t\t}, 1000)\r\n\t\t},\r\n\t\tstartHeartBeat() {\r\n\t\t\t// 每15s推送一次心跳\r\n\t\t\tthis.heartbeatTimer && clearInterval(this.heartbeatTimer);\r\n\t\t\tthis.heartbeatTimer = setInterval(() => {\r\n\t\t\t\tthis.API.heartbeat(this.friend.id);\r\n\t\t\t}, 15000)\r\n\t\t},\r\n\t\tsendCandidate() {\r\n\t\t\tthis.candidates.forEach((candidate) => {\r\n\t\t\t\tthis.API.sendCandidate(this.friend.id, candidate);\r\n\t\t\t});\r\n\t\t\tthis.candidates = [];\r\n\t\t},\r\n\t\tinitEvent() {\r\n\t\t\tthis.uniEvent.listen((key, data) => {\r\n\t\t\t\tconsole.log(\"来自app的消息：\" + key + \":\" + JSON.stringify(data));\r\n\t\t\t\tif (key == \"RTC_MESSAGE\") {\r\n\t\t\t\t\t// RTC信令\r\n\t\t\t\t\tthis.onRTCMessage(data);\r\n\t\t\t\t} else if (key == \"NAV_BACK\") {\r\n\t\t\t\t\tthis.onNavBack();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tconnected(localStream) {\r\n\t\t\tconsole.log(\"webrtc开始连接\")\r\n\t\t\tthis.localStream = localStream;\r\n\t\t\t// 显示本地视频\r\n\t\t\tthis.$refs.mineVideo.srcObject = localStream;\r\n\t\t\tdocument.getElementById('mineVideo').muted = true;\r\n\t\t\tthis.$refs.mineVideo.play().catch(() => {\r\n\t\t\t\tconsole.log(\"本地流播放异常\")\r\n\t\t\t});\r\n\t\t\t// 建立webrtc连接\r\n\t\t\tthis.webrtc.setupPeerConnection(localStream, (remoteStream) => {\r\n\t\t\t\t// 对方视频流\r\n\t\t\t\tconsole.log(\"获取到远端流\")\r\n\t\t\t\tthis.remoteStream = remoteStream;\r\n\t\t\t\tthis.$refs.friendVideo.srcObject = remoteStream;\r\n\t\t\t\tthis.$refs.friendVideo.play().catch(() => {\r\n\t\t\t\t\tconsole.log(\"远端流播放异常\")\r\n\t\t\t\t});\r\n\t\t\t})\r\n\t\t\t// 监听候选信息\r\n\t\t\tthis.webrtc.onIcecandidate((candidate) => {\r\n\t\t\t\tif (this.state == \"CHATING\") {\r\n\t\t\t\t\t// 连接已就绪,直接发送\r\n\t\t\t\t\tthis.API.sendCandidate(this.friend.id, candidate);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 连接未就绪,缓存起来，连接后再发送\r\n\t\t\t\t\tthis.candidates.push(candidate)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t// 监听连接成功状态\r\n\t\t\tthis.webrtc.onStateChange((state) => {\r\n\t\t\t\tif (state == \"connected\") {\r\n\t\t\t\t\tthis.onConnected();\r\n\t\t\t\t} else if (state == \"disconnected\") {\r\n\t\t\t\t\tthis.onDisconnected();\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\trefreshTitle() {\r\n\t\t\tlet strTitle = this.mode == \"video\" ? \"视频通话\" : \"语音通话\";\r\n\t\t\tif (this.friend.showNickName) {\r\n\t\t\t\tstrTitle += \"-\" + this.friend.showNickName\r\n\t\t\t}\r\n\t\t\tdocument.title = strTitle;\r\n\t\t},\r\n\t\tcheckDevEnable() {\r\n\t\t\t// 检测摄像头\r\n\t\t\tif (!this.camera.isEnable()) {\r\n\t\t\t\tconsole.log(\"未检测到摄像头...\")\r\n\t\t\t\tthis.state = \"ERROR\";\r\n\t\t\t\tthis.tip = \"未检测到摄像头\";\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t// 检测webrtc\r\n\t\t\tif (!this.webrtc.isEnable()) {\r\n\t\t\t\tconsole.log(\"初始化RTC失败...\")\r\n\t\t\t\tthis.state = \"ERROR\";\r\n\t\t\t\tthis.tip = \"初始化RTC失败，原因可能是: 1.服务器缺少ssl证书 2.您的设备不支持WebRTC\";\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t},\r\n\t\tinitRtc(callback) {\r\n\t\t\tconsole.log(\"初始化webrtc...\")\r\n\t\t\t// 初始化webrtc\r\n\t\t\tconst configuration = {\r\n\t\t\t\ticeServers: this.config.iceServers\r\n\t\t\t};\r\n\t\t\tthis.webrtc.init(configuration);\r\n\t\t\tif (this.mode == \"video\") {\r\n\t\t\t\t// 打开摄像头\r\n\t\t\t\tthis.camera.openVideo(this.isFacing).then((localStream) => {\r\n\t\t\t\t\tconsole.log(\"流打开成功\")\r\n\t\t\t\t\tthis.connected(localStream);\r\n\t\t\t\t\tcallback();\r\n\t\t\t\t}).catch((e) => {\r\n\t\t\t\t\tconsole.log(\"流打开失败:\" + e.message)\r\n\t\t\t\t\tthis.tip = e.message;\r\n\t\t\t\t\t// 失败也进行连接，主要为了方便本地调试\r\n\t\t\t\t\tthis.connected();\r\n\t\t\t\t\tcallback();\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\t// 打开麦克风\r\n\t\t\t\tthis.camera.openAudio().then((localStream) => {\r\n\t\t\t\t\tconsole.log(\"流打开成功\")\r\n\t\t\t\t\tthis.connected(localStream);\r\n\t\t\t\t\tcallback();\r\n\t\t\t\t}).catch((e) => {\r\n\t\t\t\t\tconsole.log(\"流打开失败:\" + e.message)\r\n\t\t\t\t\tthis.tip = e.message;\r\n\t\t\t\t\t// 失败也进行连接，主要为了方便本地调试\r\n\t\t\t\t\tthis.connected();\r\n\t\t\t\t\tcallback();\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tclose(tip) {\r\n\t\t\tthis.tip = tip;\r\n\t\t\t// 停止呼叫语音\r\n\t\t\tthis.$refs.callAudio.pause();\r\n\t\t\t// 播放挂断语音\r\n\t\t\tthis.$refs.handupAudio.play().catch((e) => {\r\n\t\t\t\tconsole.log(\"播放挂断语音失败\")\r\n\t\t\t});\r\n\t\t\t// 清理定时器\r\n\t\t\tthis.chatTimer && clearInterval(this.chatTimer);\r\n\t\t\tthis.heartbeatTimer && clearInterval(this.heartbeatTimer);\r\n\t\t\t// 返回APP,延迟1500ms，让用户看到相关提示信息\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\t// 关闭状态\r\n\t\t\t\tthis.state = \"CLOSE\";\r\n\t\t\t\t// 关闭摄像头等资源\r\n\t\t\t\tthis.camera.close();\r\n\t\t\t\tthis.webrtc.close();\r\n\t\t\t\tthis.$refs.mineVideo.srcObject = null;\r\n\t\t\t\tthis.$refs.friendVideo.srcObject = null;\r\n\t\t\t\t// 手动推消息到uniapp端进行关闭\r\n\t\t\t\twindow.uni.postMessage({\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tkey: \"WV_CLOSE\"\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}, 1500)\r\n\t\t},\r\n\t\tdecodeURL(val) {\r\n\t\t\treturn JSON.parse(decodeURIComponent(val));\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tisConnected() {\r\n\t\t\treturn this.state == \"CHATING\";\r\n\t\t},\r\n\t\tisVideoMode() {\r\n\t\t\treturn this.mode == \"video\";\r\n\t\t},\r\n\t\tisVoiceMode() {\r\n\t\t\treturn this.mode == \"voice\";\r\n\t\t},\r\n\t\tchatTimeString() {\r\n\t\t\tlet min = Math.floor(this.chatTime / 60);\r\n\t\t\tlet sec = this.chatTime % 60;\r\n\t\t\tlet strTime = min < 10 ? \"0\" : \"\";\r\n\t\t\tstrTime += min;\r\n\t\t\tstrTime += \":\"\r\n\t\t\tstrTime += sec < 10 ? \"0\" : \"\";\r\n\t\t\tstrTime += sec;\r\n\t\t\treturn strTime;\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\t// 模式：视频通话/语音通话\r\n\t\tconst url = new URL(window.location.href);\r\n\t\tthis.mode = url.searchParams.get(\"mode\");\r\n\t\tthis.isHost = JSON.parse(url.searchParams.get(\"isHost\"))\r\n\t\tthis.baseUrl = url.searchParams.get(\"baseUrl\")\r\n\t\tthis.userId = url.searchParams.get(\"userId\")\r\n\t\tthis.loginInfo = this.decodeURL(url.searchParams.get(\"loginInfo\"));\r\n\t\tthis.friend = this.decodeURL(url.searchParams.get(\"friend\"));\r\n\t\tthis.config = this.decodeURL(url.searchParams.get(\"config\"));\r\n\t\t// 刷新标题\r\n\t\tthis.refreshTitle();\r\n\t\t// 创建API对象\r\n\t\tthis.API = new ImApi(this.baseUrl, this.loginInfo);\r\n\t\t// 初始化来自uniapp的事件\r\n\t\tthis.initEvent();\r\n\t\t// 开启心跳定时\r\n\t\tthis.startHeartBeat();\r\n\t\t// 待触发 `UniAppJSBridgeReady` 事件后，即可调用 uni 的 API。\r\n\t\tdocument.addEventListener('UniAppJSBridgeReady', () => {\r\n\t\t\twindow.uni.getEnv((env) => {\r\n\t\t\t\tconsole.log('当前环境：' + JSON.stringify(env));\r\n\t\t\t\tthis.env = env;\r\n\t\t\t});\r\n\t\t\t// 通知APP，web-view 已就绪\r\n\t\t\twindow.uni.postMessage({\r\n\t\t\t\tdata: {\r\n\t\t\t\t\tkey: \"WV_READY\"\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// 本事件触发之前不能加载任何图片,否则一旦图片加载失败，会阻塞很长时间\r\n\t\t\tthis.isJSBridgeReady = true;\r\n\t\t\t// 如果是呼叫方，直接开始初始化RTC\r\n\t\t\tif (this.isHost && this.checkDevEnable()) {\r\n\t\t\t\tthis.initRtc(() => {\r\n\t\t\t\t\t// 呼叫方必须成功打开本地流才能正常呼叫\r\n\t\t\t\t\tif (this.localStream) {\r\n\t\t\t\t\t\tthis.onCall();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.mask {\r\n\tposition: absolute;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tfilter: blur(50px);\r\n\tz-index: 0;\r\n}\r\n\r\n.chat-video {\r\n\tposition: fixed;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tbackground-size: 100% 100%;\r\n\toverflow: hidden;\r\n\tbackground: linear-gradient(145deg, #ffffff20 25%, #00000060), #333;\r\n\r\n\r\n\t.friend-avatar {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\tpadding-top: 200px;\r\n\r\n\t\t.friend-name {\r\n\t\t\tmargin-top: 5px;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 40px;\r\n\t\t\tcolor: black;\r\n\t\t}\r\n\t}\r\n\r\n\t.video-box {\r\n\t\tposition: relative;\r\n\t\theight: 100%;\r\n\r\n\t\t.video-mine {\r\n\t\t\tposition: fixed;\r\n\t\t\tz-index: 9999;\r\n\t\t\ttop: 20px;\r\n\t\t\tright: 20px;\r\n\t\t\tbackground-color: grey;\r\n\r\n\t\t\t.video-mine-video {\r\n\t\t\t\twidth: 200px;\r\n\t\t\t\tmin-height: 200px;\r\n\t\t\t\tmax-height: 500px;\r\n\t\t\t\tobject-fit: cover;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\r\n\t\t\t.reverse {\r\n\t\t\t\t// 本地画面翻转: 开启后部分IOS手机会出现黑屏，暂时屏蔽\r\n\t\t\t\t// transform: rotateY(180deg);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.video-friend {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground-color: darkgray;\r\n\r\n\t\t\t.video-friend-video {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tobject-fit: cover;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tip {\r\n\t\tposition: fixed;\r\n\t\tbottom: 360px;\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\r\n\t\t.tip-text {\r\n\t\t\tfont-size: 30px;\r\n\t\t\tpadding: 12px;\r\n\t\t\tcolor: white;\r\n\t\t\tbackground: #303030a0;\r\n\t\t\tborder-radius: 10px;\r\n\t\t}\r\n\t}\r\n\r\n\t.chat-time {\r\n\t\tposition: fixed;\r\n\t\tbottom: 260px;\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\r\n\t\t.chat-time-text {\r\n\t\t\tfont-size: 35px;\r\n\t\t\tpadding: 10px;\r\n\t\t\tcolor: white;\r\n\t\t\tbackground: #303030a0;\r\n\t\t\tborder-radius: 10px;\r\n\t\t}\r\n\t}\r\n\r\n\t.control-bar {\r\n\t\tposition: fixed;\r\n\t\tbottom: 80px;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 20px;\r\n\t\twidth: 100%;\r\n\r\n\t\t.icon-rtc {\r\n\t\t\tcolor: white;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tpadding: 20px;\r\n\t\t\tfont-size: 100px;\r\n\t\t\tbackground-color: #1ba609;\r\n\r\n\t\t\t&.red {\r\n\t\t\t\tbackground-color: #e61d1d;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.icon-tool {\r\n\t\t\tcolor: white;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tpadding: 20px;\r\n\t\t\tfont-size: 80px;\r\n\t\t\tbackground-color: #4f4f4f;\r\n\t\t}\r\n\t}\r\n}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ChatVideo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ChatVideo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ChatVideo.vue?vue&type=template&id=4e2bfdb2&scoped=true\"\nimport script from \"./ChatVideo.vue?vue&type=script&lang=js\"\nexport * from \"./ChatVideo.vue?vue&type=script&lang=js\"\nimport style0 from \"./ChatVideo.vue?vue&type=style&index=0&id=4e2bfdb2&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4e2bfdb2\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div id=\"app\">\r\n    <chat-video></chat-video>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ChatVideo from \"./view/ChatVideo.vue\"\r\n\r\nexport default {\r\n  name: 'App',\r\n  components:{\r\n\t  ChatVideo\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t#app{\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--13-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--13-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=58d4d68d\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\nimport style0 from \"./App.vue?vue&type=style&index=0&id=58d4d68d&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "\r\nconst MESSAGE_TYPE = {\r\n\tRTC_SETUP_VOICE: 100,\r\n\tRTC_SETUP_VIDEO: 101,\r\n\tRTC_ACCEPT: 102,\r\n\tRTC_REJECT: 103,\r\n\tRTC_CANCEL: 104,\r\n\tRTC_FAILED: 105,\r\n\tRTC_HANDUP: 106,\r\n\tRTC_OFFER: 107,\r\n\tRTC_ANSWER: 108,\r\n\tRTC_CANDIDATE: 109,\r\n}\r\n\r\nexport {\r\n\tMESSAGE_TYPE\r\n}\r\n", "import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport * as enums from './common/enums';\r\nimport './assets/iconfont/iconfont.css';\r\n// 开发时打开\r\n//import VConsole from './vconsole' \r\n\r\nVue.prototype.$enums = enums\r\nVue.config.productionTip = false\r\n\r\nnew Vue({\r\n\trender: h => h(App)\r\n}).$mount('#app')", "export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&id=58d4d68d&prod&lang=scss\"", "module.exports = __webpack_public_path__ + \"media/handup.74a77455.wav\";", "module.exports = __webpack_public_path__ + \"media/call.038ab63f.wav\";", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HeadImage.vue?vue&type=style&index=0&id=4694a9a0&prod&scoped=true&lang=scss\""], "sourceRoot": ""}