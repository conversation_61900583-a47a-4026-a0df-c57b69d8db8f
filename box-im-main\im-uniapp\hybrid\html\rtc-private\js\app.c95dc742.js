(function(e){function t(t){for(var n,c,r=t[0],a=t[1],d=t[2],l=0,u=[];l<r.length;l++)c=r[l],Object.prototype.hasOwnProperty.call(o,c)&&o[c]&&u.push(o[c][0]),o[c]=0;for(n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n]);h&&h(t);while(u.length)u.shift()();return s.push.apply(s,d||[]),i()}function i(){for(var e,t=0;t<s.length;t++){for(var i=s[t],n=!0,r=1;r<i.length;r++){var a=i[r];0!==o[a]&&(n=!1)}n&&(s.splice(t--,1),e=c(c.s=i[0]))}return e}var n={},o={app:0},s=[];function c(t){if(n[t])return n[t].exports;var i=n[t]={i:t,l:!1,exports:{}};return e[t].call(i.exports,i,i.exports,c),i.l=!0,i.exports}c.m=e,c.c=n,c.d=function(e,t,i){c.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},c.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.t=function(e,t){if(1&t&&(e=c(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(c.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)c.d(i,n,function(t){return e[t]}.bind(null,n));return i},c.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return c.d(t,"a",t),t},c.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},c.p="";var r=window["webpackJsonp"]=window["webpackJsonp"]||[],a=r.push.bind(r);r.push=t,r=r.slice();for(var d=0;d<r.length;d++)t(r[d]);var h=a;s.push([0,"chunk-vendors"]),i()})({0:function(e,t,i){e.exports=i("56d7")},"0739":function(e,t,i){"use strict";i("1a2d1")},"1a2d1":function(e,t,i){},"47bf":function(e,t,i){},"56d7":function(e,t,i){"use strict";i.r(t);var n={};i.r(n),i.d(n,"MESSAGE_TYPE",(function(){return J}));var o=i("2b0e"),s=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("chat-video")],1)},c=[],r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"chat-video"},[e.isJSBridgeReady?t("div",{staticClass:"mask",style:{backgroundImage:"url("+e.friend.headImage+")"}}):t("div",{staticClass:"mask"}),t("audio",{ref:"callAudio",attrs:{loop:"true"}},[t("source",{attrs:{src:i("cffd")}})]),t("audio",{ref:"handupAudio",attrs:{"x5-playsinline":"",playsinline:"","webkit-playsinline":""}},[t("source",{attrs:{src:i("95cc")}})]),t("div",{directives:[{name:"show",rawName:"v-show",value:!e.isConnected||e.isVoiceMode,expression:"!isConnected||isVoiceMode"}],staticClass:"friend-avatar"},[t("head-image",{attrs:{isReady:e.isJSBridgeReady,url:e.friend.headImage,name:e.friend.showNickName,size:200}},[t("div",{staticClass:"friend-name"},[e._v(e._s(e.friend.showNickName))])])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isConnected&&e.isVideoMode,expression:"isConnected&&isVideoMode"}],staticClass:"video-box"},[t("div",{staticClass:"video-friend"},[t("video",{ref:"friendVideo",staticClass:"video-friend-video",attrs:{id:"friendVideo","x5-video-player-fullscreen":"true","x5-playsinline":"",playsinline:"","webkit-playsinline":""}})]),t("div",{staticClass:"video-mine"},[t("video",{ref:"mineVideo",staticClass:"video-mine-video",class:e.isFacing?"reverse":"",attrs:{id:"mineVideo",muted:"","x5-video-player-fullscreen":"true","x5-playsinline":"",playsinline:"","webkit-playsinline":""},domProps:{muted:!0}})])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.tip,expression:"tip"}],staticClass:"tip"},[t("span",{staticClass:"tip-text"},[e._v(e._s(e.tip))])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isConnected,expression:"isConnected"}],staticClass:"chat-time"},[t("span",{staticClass:"chat-time-text"},[e._v(e._s(e.chatTimeString))])]),t("div",{staticClass:"control-bar"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.isConnected&&e.isMicroPhone,expression:"isConnected && isMicroPhone"}],staticClass:"icon iconfont icon-microphone-on icon-tool",on:{click:function(t){return e.onSwitchMicroPhone()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isConnected&&!e.isMicroPhone,expression:"isConnected && !isMicroPhone"}],staticClass:"icon iconfont icon-microphone-off icon-tool",on:{click:function(t){return e.onSwitchMicroPhone()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:!e.isHost&&!e.isConnected,expression:"!isHost && !isConnected"}],staticClass:"icon iconfont icon-phone-reject icon-rtc red",on:{click:function(t){return e.onReject()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:!e.isHost&&!e.isConnected,expression:"!isHost && !isConnected"}],staticClass:"icon iconfont icon-phone-accept icon-rtc",on:{click:function(t){return e.onAccept()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isHost&&!e.isConnected,expression:"isHost && !isConnected"}],staticClass:"icon iconfont icon-phone-reject icon-rtc red",on:{click:function(t){return e.onCancel()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isConnected,expression:"isConnected"}],staticClass:"icon iconfont icon-phone-reject icon-150 icon-rtc red",on:{click:function(t){return e.onHandup()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isConnected&&e.isVideoMode&&e.isFacing,expression:"isConnected && isVideoMode && isFacing"}],staticClass:"icon iconfont icon-camera-front icon-tool",on:{click:function(t){return e.onSwitchCamera()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isConnected&&e.isVideoMode&&!e.isFacing,expression:"isConnected && isVideoMode && !isFacing"}],staticClass:"icon iconfont icon-camera-back icon-tool",on:{click:function(t){return e.onSwitchCamera()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isConnected&&e.isVoiceMode&&e.isSpeaker,expression:"isConnected && isVoiceMode && isSpeaker"}],staticClass:"icon iconfont icon-speaker-on icon-tool",on:{click:function(t){return e.onSwitchSpeaker()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isConnected&&e.isVoiceMode&&!e.isSpeaker,expression:"isConnected && isVoiceMode && !isSpeaker"}],staticClass:"icon iconfont icon-speaker-off icon-tool",on:{click:function(t){return e.onSwitchSpeaker()}}})])])},a=[],d=(i("14d9"),i("e9f5"),i("7d54"),i("88a7"),i("271a"),i("5494"),i("cee4"));let h=function(e,t){window.plus&&(window.XMLHttpRequest=window.plus.net.XMLHttpRequest);const i=d["a"].create({baseURL:e,timeout:3e4,headers:{"Content-Type":"application/json; charset=utf-8"}});return i.interceptors.request.use(e=>{let i=t.accessToken;return i&&(e.headers.accessToken=encodeURIComponent(i)),e},e=>Promise.reject(e)),i.interceptors.response.use(async e=>{if(200==e.data.code)return e.data.data;if(401==e.data.code){console.log("token失效，尝试重新获取");let n=t.refreshToken;return t=await i({method:"put",url:"/refreshToken",headers:{refreshToken:n}}).catch(()=>{console.log("服务器请求异常")}),"object"!=typeof e.config.data&&(e.config.headers=void 0),i(e.config)}return Promise.reject(e.data)},e=>(console.log("服务器出了点小差，请稍后再试"),Promise.reject(e))),i};var l=h;class u{}u.prototype.isEnable=function(){return window.RTCPeerConnection=window.RTCPeerConnection||window.webkitRTCPeerConnection||window.mozRTCPeerConnection,window.RTCSessionDescription=window.RTCSessionDescription||window.webkitRTCSessionDescription||window.mozRTCSessionDescription,window.RTCIceCandidate=window.RTCIceCandidate||window.webkitRTCIceCandidate||window.mozRTCIceCandidate,!!window.RTCPeerConnection},u.prototype.init=function(e){this.configuration=e,this.isAndroid11()&&this.fixAndroid()},u.prototype.setupPeerConnection=function(e,t){this.peerConnection=new RTCPeerConnection(this.configuration),this.peerConnection.ontrack=e=>{t(e.streams[0])},e&&e.getTracks().forEach(t=>{this.peerConnection.addTrack(t,e)})},u.prototype.switchStream=function(e){let t=this.peerConnection.getSenders(),i=e.getVideoTracks()[0],n=e.getAudioTracks()[0];t.forEach(e=>{e.track&&"video"==e.track.kind&&e.replaceTrack(i),e.track&&"audio"==e.track.kind&&e.replaceTrack(n)})},u.prototype.onIcecandidate=function(e){this.peerConnection.onicecandidate=t=>{t.candidate&&e(t.candidate)}},u.prototype.onStateChange=function(e){this.peerConnection.oniceconnectionstatechange=t=>{let i=t.target.iceConnectionState;console.log("ICE连接状态变化: : "+i),e(i)}},u.prototype.createOffer=function(){return new Promise((e,t)=>{const i={offerToRecieveAudio:1,offerToRecieveVideo:1};this.peerConnection.createOffer(i).then(t=>{this.peerConnection.setLocalDescription(t),e(t)}).catch(e=>{t(e)})})},u.prototype.createAnswer=function(e){return new Promise((t,i)=>{this.setRemoteDescription(e);const n={offerToRecieveAudio:1,offerToRecieveVideo:1};this.peerConnection.createAnswer(n).then(e=>{this.peerConnection.setLocalDescription(e),t(e)}).catch(e=>{i(e)})})},u.prototype.setRemoteDescription=function(e){this.peerConnection.setRemoteDescription(new RTCSessionDescription(e))},u.prototype.addIceCandidate=function(e){this.peerConnection.addIceCandidate(new RTCIceCandidate(e))},u.prototype.close=function(e){this.peerConnection&&(this.peerConnection.close(),this.peerConnection.onicecandidate=null,this.peerConnection.onaddstream=null)},u.prototype.isAndroid11=function(){if(window.plus){const e=navigator.userAgent,t=e.match(/Android ([\d.]+)/);if(t&&2===t.length)return console.log("androidVersion:",t),"11"==t[1]}return!1},u.prototype.fixAndroid=function(){console.log("fixAndroid close"),this.configuration.iceCandidatePoolSize=1;let e=new RTCPeerConnection(this.configuration);e.createOffer({offerToReceiveAudio:!0,offerToReceiveVideo:!0}).then(t=>{e.setLocalDescription(t),setTimeout(()=>{e.close(),console.log("fixAndroid close")},1e3)})};var p=u;const f=(e,t)=>new Promise((t,i)=>{window.plus.android.requestPermissions(["android.permission."+e],(function(e){console.log(e,"resultObj"),t(e)}),(function(e){i()}))});let m;const C=(e,t)=>{m=new window.plus.nativeObj.View("topMessageView",{width:"100%",height:"100%",backgroundColor:"rgba(0,0,0,0.85)"}),m.drawText(e,{top:"-50px",left:"10%",width:"80%",height:"80%"},{color:"#ffffff",size:"22px"}),m.drawText(t,{top:"0px",left:"10%",width:"80%",height:"80%"},{color:"#ffffff",whiteSpace:"normal"}),m.show()},w=async(e,t,i,n)=>{if(window.plus&&"iOS"!==window.plus.os.name){let o=setTimeout(()=>C(t,i),300),s=await f(e,n);if(clearTimeout(o),m&&m.close(),console.log("res:",s),!s.granted[0])return!1}return!0},v=async()=>{const e="WRITE_EXTERNAL_STORAGE",t="访问媒体和文件权限说明",i="用于用户发送图片、视频、文件或上传头像等场景",n="访问媒体和文件权限未获得,此权限用于用户发送图片、视频、文件或上传头像等场景,请前往设置中打开";return w(e,t,i,n)},g=async()=>{const e="CAMERA",t="相机使用权限说明",i="用于拍照、录像、视频通话等场景",n="相机使用权限未获得,此权限用于拍照、录像、视频通话等场景,请前往设置中打开";return w(e,t,i,n)},T=async()=>{const e="RECORD_AUDIO",t="麦克风使用权限说明",i="用于拍摄、录制语音消息、视频或语音通话等场景",n="麦克风使用权限未获得,此权限用于用于拍摄、录制语音消息、视频或语音通话等场景,请前往设置中打开";return w(e,t,i,n)};var b={storage:v,camera:g,micro:T};class R{constructor(){this.stream=null}}R.prototype.isEnable=function(){return!!navigator&&!!navigator.mediaDevices&&!!navigator.mediaDevices.getUserMedia},R.prototype.openVideo=async function(e){return this.stream&&this.close(),new Promise((t,i)=>{setTimeout(async()=>{if(!await b.camera())return i({message:"未能获取摄像头访问权限"});if(!await b.micro())return i({message:"未能获取麦克风权限"});let n=window.devicePixelRatio||1,o=e?"user":"environment",s={video:{width:window.screen.height*n,height:window.screen.width*n,facingMode:o},audio:{echoCancellation:!0,noiseSuppression:!0}};navigator.mediaDevices.getUserMedia(s).then(e=>{console.log("摄像头打开"),this.stream=e,t(e)}).catch(e=>{console.log("摄像头未能正常打开",e),i({code:0,message:"摄像头未能正常打开"})})})})},R.prototype.openAudio=function(){return this.stream&&this.close(),new Promise((e,t)=>{setTimeout(async()=>{if(!await b.micro())return t({code:0,message:"未能获取麦克风权限"});let i={video:!1,audio:{echoCancellation:!0,noiseSuppression:!0}};navigator.mediaDevices.getUserMedia(i).then(t=>{this.stream=t,e(t)}).catch(()=>{console.log("麦克风未能正常打开"),t({code:0,message:"麦克风未能正常打开"})})})})},R.prototype.close=function(){this.stream&&(this.stream.getTracks().forEach(e=>{e.stop()}),this.stream=null)};var S=R;class y{constructor(e,t){this.http=l(e,t)}}y.prototype.setup=function(e,t){return this.http({url:`/webrtc/private/setup?uid=${e}&mode=${t}`,method:"post"})},y.prototype.accept=function(e){return this.http({url:"/webrtc/private/accept?uid="+e,method:"post"})},y.prototype.handup=function(e){return this.http({url:"/webrtc/private/handup?uid="+e,method:"post"})},y.prototype.cancel=function(e){return this.http({url:"/webrtc/private/cancel?uid="+e,method:"post"})},y.prototype.reject=function(e){return this.http({url:"/webrtc/private/reject?uid="+e,method:"post"})},y.prototype.failed=function(e,t){return this.http({url:`/webrtc/private/failed?uid=${e}&reason=${t}`,method:"post"})},y.prototype.offer=function(e,t){return this.http({url:"/webrtc/private/offer?uid="+e,method:"post",data:JSON.stringify(t)})},y.prototype.answer=function(e,t){return this.http({url:"/webrtc/private/answer?uid="+e,method:"post",data:JSON.stringify(t)})},y.prototype.sendCandidate=function(e,t){return this.http({url:"/webrtc/private/candidate?uid="+e,method:"post",data:JSON.stringify(t)})},y.prototype.heartbeat=function(e){return this.http({url:"/webrtc/private/heartbeat?uid="+e,method:"post"})};var E=y;class A{}A.prototype.listen=function(e){window.onEvent=t=>{let i=JSON.parse(decodeURIComponent(t));e(i.key,i.data)},window.addEventListener("message",(function(t){const i=t.data;e(i.key,i.data)}),!1)};var k=A,P=function(){var e=this,t=e._self._c;return t("div",{staticClass:"head-image"},[e.isReady&&e.url?t("img",{staticClass:"avatar-image",attrs:{src:e.url}}):t("div",{staticClass:"avatar-text",style:e.avatarTextStyle},[e._v(" "+e._s(e.name.substring(0,1).toUpperCase())+" ")]),e._t("default")],2)},I=[],_={name:"headImage",data(){return{colors:["#7dd24b","#c7515a","#db68ef","#15d29b","#85029b","#c9b455","#fb2609","#bda818","#af0831","#326eb6"]}},props:{id:{type:Number},size:{type:Number,default:50},url:{type:String},name:{type:String,default:"?"},isReady:{type:Boolean,default:!0}},methods:{},computed:{avatarTextStyle(){return`color:${this.textColor};`},textColor(){let e=0;for(var t=0;t<this.name.length;t++)e+=this.name.charCodeAt(t);return this.colors[e%this.colors.length]}}},M=_,N=(i("fa7a"),i("2877")),O=Object(N["a"])(M,P,I,!1,null,"4694a9a0",null),V=O.exports,x={data(){return{env:{},isJSBridgeReady:!1,mode:"video",camera:new S,webrtc:new p,uniEvent:new k,API:null,localStream:null,remoteStream:null,isHost:!0,isMicroPhone:!0,isFacing:!0,isSpeaker:!0,isLock:!1,tip:"",state:"INIT",candidates:[],chatTime:0,audioPlayer:null,disconnectTimer:null,chatTimer:null,heartbeatTimer:null,baseUrl:"",loginInfo:{},userId:null,friend:{},config:{}}},components:{HeadImage:V},methods:{onSwitchMicroPhone(){this.isMicroPhone=!this.isMicroPhone,this.localStream&&this.localStream.getTracks().forEach(e=>{"audio"===e.kind&&(e.enabled=this.isMicroPhone)}),console.log("麦克风:"+this.isMicroPhone)},onSwitchCamera(){this.isFacing=!this.isFacing,this.camera.openVideo(this.isFacing).then(e=>{this.localStream=e,this.isMicroPhone||this.localStream.getTracks().forEach(e=>{"audio"===e.kind&&(e.enabled=!1)}),this.webrtc.switchStream(e);let t=this.isFacing?"前置":"后置";this.$refs.mineVideo.srcObject=e,this.$refs.mineVideo.play().catch(()=>{console.log("播放本地视频失败")}),console.log("摄像头切换:"+t)}).catch(e=>{console.log("摄像头切换失败:"+e.message)})},onSwitchSpeaker(){this.isSpeaker=!this.isSpeaker;const e=document.getElementById("friendVideo");e.pause(),e.muted=!this.isSpeaker,e.play(),console.log("扬声器切换:"+this.isSpeaker)},onCall(){this.API.setup(this.friend.id,this.mode).then(()=>{this.tip="等待对方接受邀请...",this.state="WAITING",this.$refs.callAudio.play().catch(e=>{console.log("播放呼叫语音失败")})}).catch(e=>{this.close(e.message)})},onAccept(){return this.tryLock()?(this.$refs.callAudio.pause(),this.checkDevEnable()?void this.initRtc(()=>{this.API.accept(this.friend.id).catch(e=>{this.tip=e.message})}):(this.API.failed(this.friend.id,"对方设备不支持通话"),!1)):(console.log("accept防抖触发"),!1)},onReject(){if(!this.tryLock())return console.log("reject防抖触发"),!1;this.API.reject(this.friend.id),this.close("您已拒绝通话")},onCancel(){if(!this.tryLock())return console.log("cancel防抖触发"),!1;this.API.cancel(this.friend.id),this.close("您已取消呼叫")},onHandup(){this.API.handup(this.friend.id),this.close("您已挂断,通话结束")},onConnected(){console.log("webrtc连接成功"),this.tip="",this.disconnectTimer&&clearTimeout(this.disconnectTimer)},onDisconnected(){console.log("webrtc网络断开"),this.tip="当前通话质量不佳",this.disconnectTimer=setTimeout(()=>{this.close("网络异常，通话结束")},15e3)},onNavBack(){console.log("强制退出"),this.isHost&&"WAITING"==this.state?(console.log("强制终止呼叫"),this.API.cancel(this.friend.id)):this.isHost||"WAITING"!=this.state?"CHATING"==this.state&&this.API.handup(this.friend.id):(console.log("强制拒绝接听"),this.API.reject(this.friend.id))},onRTCSetup(e){this.state="WAITING",this.tip=`邀请您${this.isVideoMode?"视频":"语音"}通话...`,this.$refs.callAudio.play().catch(e=>{console.log("播放呼叫语音失败")})},onRTCAccept(e){e.selfSend?this.close("已在其他设备接听"):this.webrtc.createOffer().then(e=>{this.$refs.callAudio.pause(),this.API.offer(this.friend.id,e)})},onRTCRejct(e){e.selfSend?this.close("已在其他设备拒绝通话"):this.close("对方拒绝了您的通话请求")},onRTCCancel(e){this.close("对方取消了呼叫")},onRTCFailed(e){e.selfSend?this.close("您未接听"):this.close(e.content)},onRTCHandup(e){this.close("对方已挂断,通话结束","对方已挂断")},onRTCOffer(e){const t=JSON.parse(e.content);this.webrtc.createAnswer(t).then(e=>{this.API.answer(this.friend.id,e)}),this.state="CHATING",this.startChatTime()},onRTCAnswer(e){const t=JSON.parse(e.content);this.webrtc.setRemoteDescription(t),this.state="CHATING",this.sendCandidate(),this.startChatTime()},onRTCCandidate(e){this.webrtc.addIceCandidate(JSON.parse(e.content))},onRTCMessage(e){if(e.sendId==this.friend.id||e.sendId==this.userId||e.type==this.$enums.MESSAGE_TYPE.RTC_SETUP_VIDEO||e.type==this.$enums.MESSAGE_TYPE.RTC_SETUP_VOICE)switch(e.type){case this.$enums.MESSAGE_TYPE.RTC_SETUP_VIDEO:this.onRTCSetup(e);break;case this.$enums.MESSAGE_TYPE.RTC_SETUP_VOICE:this.onRTCSetup(e);break;case this.$enums.MESSAGE_TYPE.RTC_ACCEPT:this.onRTCAccept(e);break;case this.$enums.MESSAGE_TYPE.RTC_REJECT:this.onRTCRejct(e);break;case this.$enums.MESSAGE_TYPE.RTC_CANCEL:this.onRTCCancel(e);break;case this.$enums.MESSAGE_TYPE.RTC_HANDUP:this.onRTCHandup(e);break;case this.$enums.MESSAGE_TYPE.RTC_FAILED:this.onRTCFailed(e);break;case this.$enums.MESSAGE_TYPE.RTC_OFFER:this.onRTCOffer(e);break;case this.$enums.MESSAGE_TYPE.RTC_ANSWER:this.onRTCAnswer(e);break;case this.$enums.MESSAGE_TYPE.RTC_CANDIDATE:this.onRTCCandidate(e);break}},tryLock(){return!this.isLock&&(this.isLock=!0,setTimeout(()=>{this.isLock=!1},2e3),!0)},startChatTime(){this.chatTimer&&clearInterval(this.chatTimer),this.chatTimer=setInterval(()=>{this.chatTime++},1e3)},startHeartBeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>{this.API.heartbeat(this.friend.id)},15e3)},sendCandidate(){this.candidates.forEach(e=>{this.API.sendCandidate(this.friend.id,e)}),this.candidates=[]},initEvent(){this.uniEvent.listen((e,t)=>{console.log("来自app的消息："+e+":"+JSON.stringify(t)),"RTC_MESSAGE"==e?this.onRTCMessage(t):"NAV_BACK"==e&&this.onNavBack()})},connected(e){console.log("webrtc开始连接"),this.localStream=e,this.$refs.mineVideo.srcObject=e,document.getElementById("mineVideo").muted=!0,this.$refs.mineVideo.play().catch(()=>{console.log("本地流播放异常")}),this.webrtc.setupPeerConnection(e,e=>{console.log("获取到远端流"),this.remoteStream=e,this.$refs.friendVideo.srcObject=e,this.$refs.friendVideo.play().catch(()=>{console.log("远端流播放异常")})}),this.webrtc.onIcecandidate(e=>{"CHATING"==this.state?this.API.sendCandidate(this.friend.id,e):this.candidates.push(e)}),this.webrtc.onStateChange(e=>{"connected"==e?this.onConnected():"disconnected"==e&&this.onDisconnected()})},refreshTitle(){let e="video"==this.mode?"视频通话":"语音通话";this.friend.showNickName&&(e+="-"+this.friend.showNickName),document.title=e},checkDevEnable(){return this.camera.isEnable()?!!this.webrtc.isEnable()||(console.log("初始化RTC失败..."),this.state="ERROR",this.tip="初始化RTC失败，原因可能是: 1.服务器缺少ssl证书 2.您的设备不支持WebRTC",!1):(console.log("未检测到摄像头..."),this.state="ERROR",this.tip="未检测到摄像头",!1)},initRtc(e){console.log("初始化webrtc...");const t={iceServers:this.config.iceServers};this.webrtc.init(t),"video"==this.mode?this.camera.openVideo(this.isFacing).then(t=>{console.log("流打开成功"),this.connected(t),e()}).catch(t=>{console.log("流打开失败:"+t.message),this.tip=t.message,this.connected(),e()}):this.camera.openAudio().then(t=>{console.log("流打开成功"),this.connected(t),e()}).catch(t=>{console.log("流打开失败:"+t.message),this.tip=t.message,this.connected(),e()})},close(e){this.tip=e,this.$refs.callAudio.pause(),this.$refs.handupAudio.play().catch(e=>{console.log("播放挂断语音失败")}),this.chatTimer&&clearInterval(this.chatTimer),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),setTimeout(()=>{this.state="CLOSE",this.camera.close(),this.webrtc.close(),this.$refs.mineVideo.srcObject=null,this.$refs.friendVideo.srcObject=null,window.uni.postMessage({data:{key:"WV_CLOSE"}})},1500)},decodeURL(e){return JSON.parse(decodeURIComponent(e))}},computed:{isConnected(){return"CHATING"==this.state},isVideoMode(){return"video"==this.mode},isVoiceMode(){return"voice"==this.mode},chatTimeString(){let e=Math.floor(this.chatTime/60),t=this.chatTime%60,i=e<10?"0":"";return i+=e,i+=":",i+=t<10?"0":"",i+=t,i}},mounted(){const e=new URL(window.location.href);this.mode=e.searchParams.get("mode"),this.isHost=JSON.parse(e.searchParams.get("isHost")),this.baseUrl=e.searchParams.get("baseUrl"),this.userId=e.searchParams.get("userId"),this.loginInfo=this.decodeURL(e.searchParams.get("loginInfo")),this.friend=this.decodeURL(e.searchParams.get("friend")),this.config=this.decodeURL(e.searchParams.get("config")),this.refreshTitle(),this.API=new E(this.baseUrl,this.loginInfo),this.initEvent(),this.startHeartBeat(),document.addEventListener("UniAppJSBridgeReady",()=>{window.uni.getEnv(e=>{console.log("当前环境："+JSON.stringify(e)),this.env=e}),window.uni.postMessage({data:{key:"WV_READY"}}),this.isJSBridgeReady=!0,this.isHost&&this.checkDevEnable()&&this.initRtc(()=>{this.localStream&&this.onCall()})})}},j=x,D=(i("0739"),Object(N["a"])(j,r,a,!1,null,"4e2bfdb2",null)),$=D.exports,H={name:"App",components:{ChatVideo:$}},L=H,U=(i("8ec2"),Object(N["a"])(L,s,c,!1,null,null,null)),G=U.exports;const J={RTC_SETUP_VOICE:100,RTC_SETUP_VIDEO:101,RTC_ACCEPT:102,RTC_REJECT:103,RTC_CANCEL:104,RTC_FAILED:105,RTC_HANDUP:106,RTC_OFFER:107,RTC_ANSWER:108,RTC_CANDIDATE:109};i("be35");o["a"].prototype.$enums=n,o["a"].config.productionTip=!1,new o["a"]({render:e=>e(G)}).$mount("#app")},"8ec2":function(e,t,i){"use strict";i("c4c4")},"95cc":function(e,t,i){e.exports=i.p+"media/handup.74a77455.wav"},be35:function(e,t,i){},c4c4:function(e,t,i){},cffd:function(e,t,i){e.exports=i.p+"media/call.038ab63f.wav"},fa7a:function(e,t,i){"use strict";i("47bf")}});
//# sourceMappingURL=app.c95dc742.js.map