package com.bx.implatform.controller;

import com.bx.implatform.annotation.RepeatSubmit;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.bx.implatform.service.HierarchyService;
import com.bx.implatform.session.SessionContext;
import com.bx.implatform.vo.FriendVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

//@Tag(name = "关系层级相关接口")
//@RestController
//@RequestMapping("/hierarchy")
//@RequiredArgsConstructor
//public class HierarchyController {
//
//    private final HierarchyService hierarchyService;
//
//    @GetMapping("/list")
//    @Operation(summary = "下级列表", description = "获取下级列表")
//    public Result<List<FriendVO>> findFriends() {
//        return ResultUtils.success(hierarchyService.subordinates());
//    }
//
//    @RepeatSubmit
//    @PostMapping("/addSuperior")
//    @Operation(summary = "添加上下级关系", description = "双方建立上下级关系")
//    public Result addFriend(@NotNull(message = "好友id不可为空") @RequestParam Long friendId) {
//        hierarchyService.addHierarchy(SessionContext.getSession().getUserId(),friendId);
//        return ResultUtils.success();
//    }
//
//}

