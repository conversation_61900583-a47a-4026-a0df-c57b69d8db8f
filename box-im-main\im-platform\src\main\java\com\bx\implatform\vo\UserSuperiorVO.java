package com.bx.implatform.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
@Schema(description = "用户上级VO")
public class UserSuperiorVO {

    @NotNull(message = "id不可为空")
    @Schema(description = "id")
    private String id;

    @NotNull(message = "昵称不可为空")
    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "显示名称")
    private String showNickName;

    @Schema(description = "昵称备注")
    private String remarkNickName;

    @Schema(description = "头像")
    private String headImage;

    @Schema(description = "是否在线")
    private Boolean online;

    @Schema(description = "离线时间（毫秒时间戳）")
    private Long offlineTime;

    @Schema(description = "用户来源")
    private String source;

    @Schema(description = "vip等级")
    private Integer vip;
}
