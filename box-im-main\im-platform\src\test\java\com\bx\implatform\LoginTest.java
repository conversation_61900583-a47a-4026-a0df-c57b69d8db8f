package com.bx.implatform;

import com.alibaba.fastjson.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

public class LoginTest extends BaseTest{

    public static String refreshToken(){
        String path = "/refreshToken";
        HttpHeaders headers = new HttpHeaders();
        headers.set("refreshToken", refreshToken);
        headers.set("content-type", "application/json");
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> res = restTemplate.exchange(getUrl(path), HttpMethod.PUT, entity, String.class);
        String body = res.getBody();
        System.out.println(body);
        return body;
    }
}
