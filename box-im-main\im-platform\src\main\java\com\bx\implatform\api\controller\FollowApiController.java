package com.bx.implatform.api.controller;

import com.bx.implatform.annotation.RepeatSubmit;
import com.bx.implatform.api.dto.ApiFollowDTO;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.bx.implatform.service.FollowService;
import com.bx.implatform.session.SessionContext;
import com.bx.implatform.vo.RelationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "关注相关接口")
@RestController
@RequestMapping("/api/follow")
@RequiredArgsConstructor
public class FollowApiController {

    private final FollowService followService;

    @PostMapping("/add")
    @Operation(summary = "关注", description = "关注指定用户")
    public Result<List<ApiFollowDTO>> add(@RequestBody List<ApiFollowDTO> follows) {
        return ResultUtils.success(followService.follows(follows));
    }

    @PostMapping("/remove")
    @Operation(summary = "取消关注", description = "取消关注指定用户")
    public Result<List<ApiFollowDTO>> remove(@RequestBody List<ApiFollowDTO> follows) {
        return ResultUtils.success(followService.unfollows(follows));
    }
}