package com.bx.implatform;

import com.alibaba.fastjson.JSONObject;
import com.bx.implatform.api.UserApiTest;
import com.bx.implatform.dto.FriendRequestApplyDTO;

import java.util.HashMap;
import java.util.Map;

public class FriendRequestTest extends BaseTest{

    public static void test() {
        String token = UserApiTest.token("21").getAccessToken();
        login(token);
        list();
//        apply(30L, "myFriend30");
//        apply(31L, "myFriend31");
//        list();

//        reject(15L);
//        approve(13L);
//        recall(11L);
//        list();
    }

    public static void list(){
        get("/friend/request/list", null);
    }

    public static void apply(Long friendId, String remark){
        FriendRequestApplyDTO data = new FriendRequestApplyDTO();
        data.setFriendId(friendId);
        data.setRemark(remark);
        post("/friend/request/apply", data);
    }

    public static void approve(Long id){
        Map<String,Object> map = new HashMap<>();
        map.put("id", id);
        post("/friend/request/approve", map);
    }

    public static void reject(Long id){
        Map<String,Object> map = new HashMap<>();
        map.put("id", id);
        post("/friend/request/reject", map);
    }

    public static void recall(Long id){
        Map<String,Object> map = new HashMap<>();
        map.put("id", id);
        post("/friend/request/recall", map);
    }
}
